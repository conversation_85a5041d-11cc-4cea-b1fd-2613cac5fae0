'use strict';

const Agenda = require('agenda');
const constants = require('../models/constants');
const mongoose = require('mongoose');
const { DateTime } = require('luxon');

let agenda;

const EMAIL_SP_WEBHOOK = 'email:sparkpost:webhook';
const EMAIL_SEND = 'email:sparkpost:send';
const EMAIL_BLAST_START = 'email-blast:start';

const APPLICATION_CHECK = 'application:check';
const APPLICATION_EXPIRED = 'application:expired';
const APPLICATION_DRAFT_REMINDER = 'application:draft_reminder';

const COUPON_CHECK = 'coupon:check';
const COUPON_EXPIRED = 'coupon:expired';

const REPLY_SYNC_USERS = 'reply:sync:users';
const REPLY_SYNC_CAMPAIGNS = 'reply:sync:campaigns';
const REPLY_CREATE_USER = 'reply:create:user';
const REPLY_UPDATE_USER = 'reply:update:user';
const REPLY_UPDATE_USER_EMAIL = 'reply:update:userEmail';
const REPLY_UPDATE_USER_CAMPAIGNS = 'reply:update:userCampaigns';


module.exports = {
    init,
    start,
    now,
    every,
    define,
    constants: {
        EMAILS: {
            SEND: EMAIL_SEND,
            SPARKPOST_WEBHOOK: EMAIL_SP_WEBHOOK
        },
        EMAIL_BLAST: {
            START: EMAIL_BLAST_START
        },
        APPLICATION: {
            CHECK: APPLICATION_CHECK,
            EXPIRED: APPLICATION_EXPIRED,
            DRAFT_REMINDER: APPLICATION_DRAFT_REMINDER
        },
        COUPON: {
            CHECK: COUPON_CHECK,
            EXPIRED: COUPON_EXPIRED,
        },
        REPLY: {
            SYNC_USERS: REPLY_SYNC_USERS,
            SYNC_CAMPAIGNS: REPLY_SYNC_CAMPAIGNS,
            CREATE_USER: REPLY_CREATE_USER,
            UPDATE_USER: REPLY_UPDATE_USER,
            UPDATE_USER_EMAIL: REPLY_UPDATE_USER_EMAIL,
            UPDATE_USER_CAMPAIGNS: REPLY_UPDATE_USER_CAMPAIGNS
        }
    },
    emails: {
        sendEmail: sparkpostSendEmail,
        sparkpostWebhook: sparkpostWebhookUpdateEmaillog,
        emailBlastStart: emailBlastStart
    },
    reply: {
        createUser: scheduleCreateUserJob,
        updateUser: scheduleUpdateUserJob,
        updateUserEmail: scheduleUpdateUserEmailJob,
        updateUserCampaigns: scheduleUserUpdateCampaignsJob
    }
};

function init() {
    if (agenda) {
        return agenda;
    }
    agenda = new Agenda({
        mongo: mongoose.connection
    });
    return agenda;
}

function start() {
    init();
    return new Promise((resolve, reject) => {
        agenda.on('ready', () => {
            agenda.start();
            resolve(agenda);
        });
        // eslint-disable-next-line
        agenda.on('fail', (err, job) => {
            if (job.attrs.type === 'single') {
                return;
            }
            if ((job.attrs.restartAfter && !job.attrs.restartAfter.length) || job.attrs.failCount > 3) {
                return job.disable();
            }

            try {
                const restartAfter = job.attrs.restartAfter || ['10 seconds from now', '1 minute from now', '10 minutes from now'];
                job.schedule(restartAfter.shift());
                job.attrs.restartAfter = restartAfter;
                job.save();
            }
            catch (ignore) {
                return job.disable();
            }
        });
    });
}

function now(name, data) {
    return agenda.now(name, data);
}

function every(interval, name, data, options) {
    return agenda.every(interval, name, data, options);
}

function define(name, handler) {
    init(); // Ensure agenda is initialized
    return agenda.define(name, handler);
}

async function sparkpostWebhookUpdateEmaillog(payload) {
    const job = agenda.create(EMAIL_SP_WEBHOOK, payload);
    job.attrs.nextRunAt = new Date(+job.attrs.nextRunAt + Math.trunc(1000 * 60 + Math.random() * 1000 * 60 * 5));
    await job.save();
}

// DEBUG Used for debugging
// async function getJobs() {
//     const jobs = await agenda.jobs({ type: 'email:sparkpost:send' }); // replace with your job type
//     console.log('These are the current jobs???', jobs);
// }

async function sparkpostSendEmail(payload) {
    // getJobs();
    await agenda.now(EMAIL_SEND, payload);
}
function emailBlastStart(payload) {
    agenda.now(EMAIL_BLAST_START, payload);
}

async function hasJobInFuture(name, dataFilters) {
    const query = { ...dataFilters, name, nextRunAt: { $ne: null } };
    const jobs = await agenda.jobs(query, { nextRunAt: -1 }, 1);
    return Boolean(jobs.length);
}

function randomNightDate() {
    return DateTime
        .local()
        .endOf('day')
        .plus({ seconds: Math.trunc(500 + Math.random() * 14000) })
        .toJSDate();
}
async function scheduleCreateUserJob(userKey, audiencesToAdd) {
    const hasJob = await hasJobInFuture(REPLY_CREATE_USER, { 'data.userKey': userKey });
    if (!hasJob) {
        const job = agenda.create(REPLY_CREATE_USER, { userKey, audiencesToAdd });
        job.attrs.nextRunAt = randomNightDate();
        await job.save();
    }
}
async function scheduleUpdateUserJob(userKey) {
    const query = { 'data.userKey': userKey, name: { $in: [REPLY_CREATE_USER, REPLY_UPDATE_USER] }, nextRunAt: { $ne: null } };
    const jobs = await agenda.jobs(query, { nextRunAt: -1 }, 1);
    if (!jobs.length) {
        const job = agenda.create(REPLY_UPDATE_USER, { userKey });
        job.attrs.nextRunAt = randomNightDate();
        await job.save();
    }
}
async function scheduleUpdateUserEmailJob(userKey) {
    const query = { 'data.userKey': userKey, name: REPLY_UPDATE_USER_EMAIL, nextRunAt: { $ne: null } };
    const jobs = await agenda.jobs(query, { nextRunAt: -1 }, 1);
    if (!jobs.length) {
        const job = agenda.create(REPLY_UPDATE_USER_EMAIL, { userKey });
        job.attrs.nextRunAt = randomNightDate();
        await job.save();
    }
}

async function scheduleUserUpdateCampaignsJob(userKey, audiencesToAdd, audiencesToRemove) {
    const hasJob = await hasJobInFuture(REPLY_UPDATE_USER_CAMPAIGNS, { 'data.userKey': userKey });
    if (!hasJob) {
        const job = agenda.create(REPLY_UPDATE_USER_CAMPAIGNS, { userKey, audiencesToAdd, audiencesToRemove });
        job.attrs.nextRunAt = randomNightDate();
        await job.save();
    }
}

