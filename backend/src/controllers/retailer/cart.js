'use strict';

const config = require('../../services/config');
const mongoose = require('mongoose');
const { eqVariants, getVariantFromProduct } = require('../../services/util');
const constants = require('../../models/constants');
const { canPurchaseThcProduct } = require('../../models/cart');
const Router = require('koa-router');

module.exports = Router()
    .prefix('/cart')
    .post('/', postAddProductToCart)
    .patch('/', patchUpdateCartItem)
    .delete('/', deleteProductFromCart)
    .get('/restrictions', getCartRestrictions);


async function postAddProductToCart(ctx) {
    const { Product } = mongoose.models;
    const { cart, retailer } = ctx.state;
    let { quantity, variant, product } = ctx.request.body;

    const existProduct = await Product.findOne({ key: product.key }).populate('_state');
    if (existProduct) {
        // Validate THC purchase eligibility
        const canPurchase = await canPurchaseThcProduct(existProduct, retailer);
        if (!canPurchase) {
            ctx.throw(403, 'Cannot purchase THC products from other states');
        }

        const existVariant = getVariantFromProduct(existProduct, variant);

        if (existVariant && quantity && quantity > 0 && quantity <= existVariant.quantity) {
            await cart.addProduct(existProduct, existVariant, quantity);
        }
    }

    ctx.body = {
        data: await cart.checkAndFormatPublic()
    };

}

async function getCartRestrictions(ctx) {
    const { retailer } = ctx.state;

    try {
        // Check license status
        let licenseStatus = 'valid';
        if (retailer._application) {
            await retailer.populate('_application').execPopulate();
            const application = retailer._application;

            if (!application.license || !application.license.expirationDate) {
                licenseStatus = 'missing';
            } else {
                const licenseExpiration = new Date(application.license.expirationDate);
                const now = new Date();
                if (licenseExpiration < now) {
                    licenseStatus = 'expired';
                }
            }
        }

        // For now, we'll return basic license status
        // In the future, we could track removed products and return them here
        ctx.body = {
            removedProducts: [], // This would contain products removed due to state restrictions
            licenseStatus
        };
    } catch (error) {
        ctx.throw(500, 'Error checking cart restrictions');
    }
}

async function patchUpdateCartItem(ctx) {
    const { Product } = mongoose.models;
    const { cart } = ctx.state;
    let { quantity, variant, product, index } = ctx.request.body;

    const existProduct = await Product.findOne({ key: product.key });
    if (existProduct) {
        const existVariant = getVariantFromProduct(existProduct, variant);
        if (existVariant && quantity && quantity > 0 && quantity <= existVariant.quantity) {
            await cart.updateProduct(existProduct, existVariant, quantity, index);
        }
    }

    ctx.body = {
        data: await cart.checkAndFormatPublic()
    };
}

async function deleteProductFromCart(ctx) {
    const { Product } = mongoose.models;
    const { cart } = ctx.state;
    let { variant, product } = ctx.request.body;

    const existProduct = await Product.findOne({ key: product.key });
    if (existProduct) {
        const existVariant = getVariantFromProduct(existProduct, variant);
        if (existVariant) {
            await cart.removeProduct(existProduct, existVariant);
        }
    }

    ctx.body = {
        data: await cart.checkAndFormatPublic()
    };
}


