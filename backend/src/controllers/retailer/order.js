"use strict";

const config = require("../../services/config");
const mongoose = require("mongoose");
const ObjectId = mongoose.Types.ObjectId;
const Bluebird = require("bluebird");
const constants = require("../../models/constants");
const { HttpError } = require("../../services/error");
const { eqVariants, getVariantFromProduct, paginateArray } = require("../../services/util");
const mw = require("../../services/middleware");
const Router = require("koa-router");

module.exports = Router()
  .prefix("/orders")
  .param("retailerOrderKey", paramRetailerOrder)
  .get("/", getOrderList)
  .get("/:retailerOrderKey", getOrder)
  .put(
    "/:retailerOrderKey/product-orders/:productOrderKey/review",
    putOrderReview
  )
  .put("/:retailerOrderKey/product-orders/:productOrderKey/upload-import-documents",
    uploadImportDocuments)
  .put("/:retailerOrderKey/product-orders/:productOrderKey/confirm-delivery",
    confirmDelivery)
  .put("/:retailerOrderKey/product-orders/confirm-payment", confirmPayment)
  .post("/", postOrder);

async function paramRetailerOrder(retailerOrderKey, ctx, next) {
  const { RetailerOrder } = mongoose.models;
  const retailerOrder = await RetailerOrder.findOne({
    key: retailerOrderKey,
  }).populate("_retailer");

  if (!retailerOrder) {
    throw new HttpError(404, "Retailer order not found");
  }
  ctx.state.retailerOrder = retailerOrder;

  await next();
}

async function getOrderList(ctx, next) {
  const { RetailerOrder } = mongoose.models;
  if (!ctx.request.headers.accept.includes("application/json")) {
    return await next();
  }
  const payload = ctx.query;
  const { retailer } = ctx.state;

  const query = { _retailer: retailer };

  try {
    let { retailerOrderList } = await Bluebird.props({
      retailerOrderList: RetailerOrder.find(query)
        .sort("-_id")
    });
    
    let ordersMissingDocs = await attachProductOrders(retailerOrderList, payload.missingDocuments === 'true' ? 1 : 0);

    // Filter retailer orders with no productOrders 
    retailerOrderList = retailerOrderList.filter(retailerOrder => {
      return retailerOrder._productOrders && retailerOrder._productOrders.length > 0
    });

    // Count the whole length of order list
    let count = retailerOrderList.length;

    // Paginate the order list
    retailerOrderList = paginateArray(retailerOrderList, payload.page, payload.perPage);    

    ctx.body = {
      data: retailerOrderList.map((retailerOrder) =>
        retailerOrder.formatRetailer()
      ),
      pagination: {
        count: count || 0,
        total: count || 0,
        page: payload.page,
        perPage: payload.perPage,
        ordersMissingDocs
      },
    };

  } catch (err) {
    console.log('there was an error getting the orders', err)
  }
}

async function getOrder(ctx, next) {
  if (!ctx.request.headers.accept.includes("application/json")) {
    return await next();
  }
  const { retailerOrder } = ctx.state;
  await attachProductOrders([retailerOrder]);
  ctx.body = {
    data: ctx.state.retailerOrder.formatRetailer(),
  };
}

// eslint-disable-next-line
async function postOrder(ctx) {
  // TODO: add Metrc Transfer function here
  const { SystemLog } = mongoose.models;
  const { Address, CCStore, Product, RetailerOrder } = mongoose.models;
  const payload = ctx.request.body;
  const { retailer, cart } = ctx.state;

  // Check if cart contains THC products and validate retailer license
  const cartItems = await cart.checkAndFormatPublic();
  const hasThcProducts = cartItems.some(item => item.product.cannabisLicense);

  if (hasThcProducts) {
    // Ensure retailer application is populated
    await retailer.populate('_application').execPopulate();

    if (!retailer._application) {
      throw new HttpError(403, 'Cannabis license application required for THC product purchases');
    }

    const application = retailer._application;

    // Check if license exists and is not expired
    if (!application.license || !application.license.expirationDate) {
      throw new HttpError(403, 'Valid cannabis license required for THC product purchases');
    }

    const licenseExpiration = new Date(application.license.expirationDate);
    const now = new Date();

    if (licenseExpiration < now) {
      throw new HttpError(403, 'Cannabis license has expired. Please renew your license to purchase THC products');
    }
  }

  if (
    !payload.deliveryAddress ||
    !ObjectId.isValid(payload.deliveryAddress.id)
  ) {
    throw new HttpError(422, "Invalid delivery address");
  }
  let deliveryAddress = await Address.findOne({
    _retailer: retailer,
    _id: payload.deliveryAddress.id,
  });
  if (!deliveryAddress) {
    throw new HttpError(422, "Invalid delivery address");
  }
  deliveryAddress = deliveryAddress.formatPublic();

  if (
    !payload.paymentMethod ||
    !payload.paymentMethod.type ||
    !constants.PAYMENT.METHODS.includes(payload.paymentMethod.type)
  ) {
    throw new HttpError(422, "Invalid payment method");
  }
  if (
    payload.paymentMethod.type === "creditCard" &&
    !ObjectId.isValid(payload.paymentMethod.id)
  ) {
    throw new HttpError(422, "Invalid payment method: credit card", {
      paymentMethod: payload.paymentMethod,
    });
  }
  let paymentMethod = {
    type: payload.paymentMethod.type,
    confirmed_status: constants.RETAILER_PAYMENT.STATUS.NO_REQUEST
  };
  let creditCard;
  if (payload.paymentMethod.type === "creditCard") {
    creditCard = await CCStore.findOne({
      _retailer: retailer,
      _id: payload.paymentMethod.id,
    });
    if (!creditCard) {
      throw new HttpError(422, "Invalid payment method: credit card");
    }
    paymentMethod = {
      ...creditCard.formatPublic(),
      _creditCard: creditCard._id,
      type: "creditCard"
    };
  }

  const coupon = payload.promoCode || {};

  await cart.populate("items._product").execPopulate();
  await cart.populate("items._product._vendor").execPopulate();
  for (let cartItem of cart.items) {
    if (!cartItem._product) {
      throw new HttpError(422, "Invalid product");
    }
    const product = cartItem._product;
    if (coupon.key && coupon._vendor.key === product._vendor.key) {
      cartItem.coupon = { ...coupon, _vendor: coupon._vendor.key };
    }

    const variant = getVariantFromProduct(product, cartItem.variant);
    if (!variant) {
      throw new HttpError(422, "Wrong cartItem", { variant: cartItem.variant });
    }
    if (
      !cartItem.quantity ||
      cartItem.quantity < 1 ||
      variant.quantity < cartItem.quantity
    ) {
      throw new HttpError(
        422,
        "Not enough quantity product: " + cartItem._product.name,
        { variant, product: cartItem._product.formatPublic() }
      );
    }
  }
  if (!cart.items.length) {
    throw new HttpError(422, "Empty cart");
  }

  const retailerOrder = await RetailerOrder.create({
    retailer,
    deliveryAddress,
    paymentMethod,
    creditCard,
    cart,
  });
  retailer.set({
    _lastDeliveryAddress: deliveryAddress.id,
    lastPaymentMethod: {
      type: paymentMethod.type,
      id: paymentMethod.id,
    },
  });
  await retailer.save();

  ctx.session.cart = [];
  ctx.body = {
    data: retailerOrder.formatRetailer(),
  };
  SystemLog.retailerOrderCreated(ctx, retailerOrder);
}

async function confirmPayment(ctx) {
  const { RetailerOrder } = mongoose.models;
  const { retailerOrder } = ctx.state;

  await RetailerOrder.confirmPayment(retailerOrder);

  ctx.body = {
    data: retailerOrder.formatRetailer(),
  };
}

async function uploadImportDocuments(ctx) {
  const { ProductOrder } = mongoose.models;
  const { retailer, retailerOrder } = ctx.state;
  const { productOrderKey } = ctx.params;
  const payload = ctx.request.body;

  const productOrder = await ProductOrder.findOne({
    key: productOrderKey,
    _retailerOrder: retailerOrder._id,
    _retailer: retailer._id,
  });
  if (!productOrder) {
    throw new HttpError(404, "Order not found");
  }

  try {
    if (productOrder.status !== constants.PRODUCT_ORDER.STATUS.REVIEW && productOrder.status !== constants.PRODUCT_ORDER.STATUS.APPROVED) {
      productOrder.set({ requiredDocuments: payload.requiredDocuments });
      await productOrder.save();
    }
  } catch (error) {
    console.log('product order importing documents upload error', error);
  }

  ctx.body = {
    data: productOrder.formatRetailer(),
  };
}


async function confirmDelivery(ctx) {
  const { ProductOrder } = mongoose.models;

  const { retailer, retailerOrder } = ctx.state;
  const { productOrderKey } = ctx.params;

  const productOrder = await ProductOrder.findOne({
    key: productOrderKey,
    _retailerOrder: retailerOrder._id,
    _retailer: retailer._id,
  });

  if (!productOrder) {
    throw new HttpError(404, "Order not found");
  }

  if (productOrder.status === constants.PRODUCT_ORDER.STATUS.SHIPPED) {
    productOrder.set({ status: constants.PRODUCT_ORDER.STATUS.DELIVERED });
    await productOrder.save();
  }

  ctx.body = {
    data: productOrder.formatRetailer(),
  };
}

async function putOrderReview(ctx) {
  const { ProductOrder, Review, Product } = mongoose.models;
  const { retailer, retailerOrder } = ctx.state;
  const { productOrderKey } = ctx.params;
  const payload = ctx.request.body;

  const productOrder = await ProductOrder.findOne({
    key: productOrderKey,
    _retailerOrder: retailerOrder._id,
    _retailer: retailer._id,
  });
  if (!productOrder) {
    throw new HttpError(404, "Order not found");
  }

  const product = await Product.findOne({ key: productOrder.product.key });

  let query = {
    _state: retailer._state,
    _retailer: retailer,
    _vendor: productOrder._vendor,
    _product: product._id,
    _productOrder: productOrder,
    _vendorOrder: productOrder._vendorOrder,
    _retailerOrder: productOrder._retailerOrder,
  };
  let update = {
    productRating: payload.productRating,
    productComment: payload.productComment || undefined,
    vendorRating: payload.vendorRating,
    vendorComment: payload.vendorComment || undefined,
  };
  let review = await Review.findOne(query);
  if (!review) {
    review = new Review(query);
  }

  review.set(update);

  await review.save();

  ctx.body = {
    data: review.formatPublic(),
  };
}

async function attachProductOrders(retailerOrders, missingDocuments = 0) {
  const { ProductOrder } = mongoose.models;
  const ids = retailerOrders.map((retailerOrder) => retailerOrder._id);

  let query = { _retailerOrder: { $in: ids } };

  let queryForMissingDocs = {
    ...query,
    $expr: {
      $and: [
        {
          $gt: [
            { $size: { $ifNull: ['$requiredDocuments', []] } },
            0
          ]
        },
        {
          $and: [
            {
              $ne: ['$status', constants.PRODUCT_ORDER.STATUS.DELIVERED]
            },
            {
              $ne: ['$status', constants.PRODUCT_ORDER.STATUS.PAIDOUT]
            },
            {
              $ne: ['$status', constants.PRODUCT_ORDER.STATUS.CANCELLED]
            },
            {
              $ne: ['$status', constants.PRODUCT_ORDER.STATUS.REJECTED]
            }
          ]
        }
      ]
    }
  };

  let productOrders;
  let productOrdersMissingDocs;
  
  if (missingDocuments === 1) {
    productOrders = await ProductOrder.find(queryForMissingDocs).populate("_vendor _retailer _review");
    productOrdersMissingDocs = [...productOrders];
  } else {
    productOrders = await ProductOrder.find(query).populate("_vendor _retailer _review");
    productOrdersMissingDocs = await ProductOrder.find(queryForMissingDocs).populate("_vendor _retailer _review");
  }

  const productOrderMap = {};
  productOrders.forEach((productOrder) => {
    if (!productOrderMap[productOrder._retailerOrder._id]) {
      productOrderMap[productOrder._retailerOrder._id] = [];
    }
    productOrderMap[productOrder._retailerOrder._id].push(productOrder);
  });

  retailerOrders.forEach((retailerOrder, idx) => {
    if (productOrderMap[retailerOrder._id] && productOrderMap[retailerOrder._id].length > 0) {
      retailerOrder._productOrders = productOrderMap[retailerOrder._id];
    }
  });

  return productOrdersMissingDocs.length;
}