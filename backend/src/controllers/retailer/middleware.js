'use strict';

const config = require('../../services/config');
const mongoose = require('mongoose');
const ObjectId = mongoose.Types.ObjectId;
const Bluebird = require('bluebird');
const { HttpError } = require('../../services/error');
const escapeRegex = require('escape-string-regexp');
const constants = require('../../models/constants');
const { eqVariants } = require('../../services/util');
const mw = require('../../services/middleware');
const Router = require('koa-router');

module.exports = {
    retailerSpa,
    calcMeta,
    filterCategories
};

async function retailerSpa(ctx, next) {
    const { user, retailer, store } = ctx.state;

    if (!(retailer && retailer.enabled) && ctx.state.store) {
        ctx.state.fakeUser = true;
    }
    if (ctx.state.fakeUser) {
        if (ctx.request.isDataRequest) {
            await next();
            if (ctx.body) {
                ctx.body.meta = { tickets: 0 };
            }
            return;

        }
        // Allow public users to see THC products - filtering will be handled in product queries
        const categories = await filterCategories(store._state, { showThc: true });

        ctx.state.serverData = {
            cart: [],
            meta: { tickets: 0 },
            retailer: {
                state: store._state
            },
            user: {},
            fakeUser: true,
            categories,
            redirectUrl: ctx.session.redirectUrl
        };
        delete ctx.session.redirectUrl;
        ctx.body = ctx.render('retailer/index.html');
        return;
    }

    if (!user) {
        return next();
    }
    if (!retailer) {
        throw new HttpError(401);
    }

    if (!retailer.enabled) {
        if (ctx.request.isDataRequest) {
            throw new HttpError(403, 'Inactive account');
        }
        return ctx.redirect('/login');
    }

    await retailer.populate('team._user _vendor _addresses _lastDeliveryAddress _application _creditCards').execPopulate();
    ctx.state.retailer = retailer;
    ctx.state.cart = await mongoose.models.Cart.findOrCreate(retailer._id);

    if (ctx.request.isDataRequest) {
        await next();
        if (ctx.body) {
            ctx.body.meta = await calcMeta(ctx);
        }
        return;

    }
    const categories = await filterCategories(retailer._state, { showThc: ctx.state.retailer.showThc });
    await ctx.state.user.getRetailersAndVendors();

    ctx.state.serverData = {
        cart: await ctx.state.cart.checkAndFormatPublic(),
        meta: await calcMeta(ctx),
        retailer: ctx.state.retailer.formatSource(),
        user: ctx.state.user.formatSource(),
        categories,
        redirectUrl: ctx.session.redirectUrl,
        paymentTypes: {
            ach: config.fee.paymentACH,
            wireTransfer: config.fee.paymentWireTransfer,
            check: config.fee.paymentCheck,
            creditCard: config.fee.paymentCreditCard
        }
    };
    delete ctx.session.redirectUrl;
    ctx.body = ctx.render('retailer/index.html');

    if (!ctx.session.shadowLogin && ctx.state.user.lastLoginBy !== constants.USER.ROLE.RETAILER) {
        ctx.state.user.set({ lastLoginBy: constants.USER.ROLE.RETAILER });
        await ctx.state.user.save();
    }
}

async function calcMeta(ctx) {
    const { Ticket, RetailerOrder, Product } = mongoose.models;
    const { retailer } = ctx.state;

    return await Bluebird.props({
        application: retailer._application && retailer._application.formatPublic && retailer._application.formatPublic() || undefined,
        orders: RetailerOrder.count({
            _retailer: retailer._id,
            status: constants.RETAILER_ORDER.STATUSES
        }),
        tickets: Ticket.count({
            _retailer: retailer._id,
            status: constants.TICKET.STATUS.OPENED
        }),
        vendors: Product.aggregate([
            {
                $group: {
                    _id: "$_vendor"
                }
            },
            {
                $lookup: {
                    from: "vendors",
                    localField: "_id",
                    foreignField: "_id",
                    as: "vendor_array"
                }
            },
            {
                $addFields: {
                    vendor: { $arrayElemAt: ['$vendor_array', 0] }
                }
            },
            {
                $match: {
                    'vendor.enabled': { $eq: true },
                    'vendor.deletedAt': { $exists: false },
                }
            },
            {
                $project: {
                    storeName: '$vendor.storeName'
                }
            }
        ])
    });
}

async function filterCategories(_state, filters = {}) {
    const { Category, Product, State } = mongoose.models;
    const productQuery = {
        status: constants.PRODUCT.STATUS.ACTIVE,
        deletedAt: { $exists: false },
        $or: [{ cannabisLicense: false }],
        'variants.quantity': { $gt: 0 }
    };
    if (filters._vendor) {
        productQuery._vendor = filters._vendor;
    }
    const categoryQuery = { cannabisLicense: false };
    if (filters.showThc) {
        // Get state information to determine if user is domestic or international
        const state = await State.findById(_state);

        if (state && !state.isDomestic) {
            // International users can see all THC products
            productQuery.$or.push({
                cannabisLicense: true
            });
        } else {
            // Domestic users can only see THC products from their state
            productQuery.$or.push({
                cannabisLicense: true,
                _state
            });
        }
        delete categoryQuery.cannabisLicense;
    }


    const existCategories = await Product.aggregate([
        {
            $match: productQuery
        },
        {
            $group: { _id: '$_category' }
        },
        {
            $lookup: {
                from: 'categories',
                localField: '_id',
                foreignField: '_id',
                as: 'cat'
            }
        },
        {
            $addFields: {
                cat: { $arrayElemAt: ['$cat', 0] }
            }
        },
        {
            $match: {
                'cat._id': { $exists: true }
            }
        },
        {
            $replaceRoot: { newRoot: '$cat' }
        },
        {
            $match: categoryQuery
        },
        {
            $sort: { _id: 1 }
        },
        {
            $project: {
                _id: 0,
                id: '$_id',
                name: 1,
                parent: 1,
                slug: 1
            }
        }
    ])
        .exec();

    const parentIds = existCategories.filter(category => category.parent).map(category => category.parent);
    const existsParentCategories = await Category.aggregate([
        {
            $match: {
                _id: { $in: parentIds }
            }
        },
        {
            $sort: { _id: 1 }
        },
        {
            $project: {
                _id: 0,
                id: '$_id',
                name: 1,
                parent: 1,
                slug: 1
            }
        }
    ]);
    return existsParentCategories.concat(existCategories);
}
