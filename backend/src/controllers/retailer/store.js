'use strict';

const config = require('../../services/config');
const mongoose = require('mongoose');
const Bluebird = require('bluebird');
const { HttpError } = require('../../services/error');
const constants = require('../../models/constants');
const Router = require('koa-router');
const { parseFilters } = require('./product');
const { filterCategories } = require('./middleware');

module.exports = Router()
    .prefix('/store')
    .param('productKey', paramProduct)
    .get('/:storeName', getVendorStore)
    .get('/:storeName/reviews', getVendorReviews)
    .get('/:storeName/products', getVendorProducts)
    .get('/:storeName/products/:productKey', getVendorProduct)
    .get('/:storeName/products/:productKey/reviews', getProductReviews);

async function paramProduct(productKey, ctx, next) {
    const { Product, State } = mongoose.models;
    const { store, user, retailer, states } = ctx.state;

    const query = {
        key: productKey,
        deletedAt: { $exists: false },
        _vendor: store._id,
        $or: [{ cannabisLicense: false }]
    };

    // Apply new THC filtering logic for individual product access
    if (user && retailer && retailer.showThc) {
        // Authenticated user with THC enabled
        const retailerState = states ? states.find(st => st.id.toString() === retailer._state.toString()) : null;

        if (retailerState && !retailerState.isDomestic) {
            // International users can see all THC products
            query.$or.push({ cannabisLicense: true });
        } else {
            // Domestic users can only see THC products from their state
            query.$or.push({
                cannabisLicense: true,
                _state: retailer._state
            });
        }
    } else if (!user || !retailer) {
        // Public users can see all THC products (no state restrictions for public viewing)
        query.$or.push({ cannabisLicense: true });
    }

    ctx.state.product = await Product.findOne(query).populate('_vendor');

    if (!ctx.state.product) {
        throw new HttpError(404);
    }

    await next();
}

async function getVendorStore(ctx) {
    const { Review } = mongoose.models;
    const { store } = ctx.state;

    if (!store) {
        throw new HttpError(404, 'Store not found');
    }
    const reviewList = await Review.find({
        productComment: { $exists: true },
        deletedAt: { $exists: false },
        _vendor: store._id
    })
        .limit(11)
        .populate('_retailer')
        .sort('-createdAt');

    // Allow public users to see THC categories - filtering will be handled in product queries
    const categories = await filterCategories(store._state, { _vendor: store._id, showThc: true });
    const formattedVendor = store.formatPublic();
    ctx.body = {
        data: {
            vendor: formattedVendor,
            categories,
            reviews: reviewList.map(formatReview)
        }
    };
}

async function getVendorReviews(ctx) {
    const { Review } = mongoose.models;
    const { store } = ctx.state;
    const payload = ctx.request.query;

    const query = {
        _vendor: store._id,
        deletedAt: { $exists: false },
        productComment: { $exists: true }
    };

    const response = await Bluebird.props({
        list: Review.find(query)
            .paginate(payload.page, payload.perPage)
            .populate('_retailer')
            .exec(),
        count: Review.count(query)
    });

    ctx.body = {
        data: response.list.map(formatReview),
        pagination: {
            total: response.count,
            page: payload.page,
            perPage: payload.perPage
        }
    };
}
async function getProductReviews(ctx) {
    const { Review } = mongoose.models;
    const { product } = ctx.state;
    const payload = ctx.request.query;

    const query = {
        _vendor: product._id,
        deletedAt: { $exists: false },
        vendorComment: { $exists: true }
    };
    const response = await Bluebird.props({
        list: Review.find(query)
            .paginate(payload.page, payload.perPage)
            .populate('_retailer')
            .exec(),
        count: Review.count(query)
    });

    ctx.body = {
        data: response.list.map(formatReview),
        pagination: {
            total: response.count,
            page: payload.page,
            perPage: payload.perPage
        }
    };
}

function formatReview(review) {
    return {
        from: review._retailer && review._retailer.businessName || review.reviewerName,
        text: review.productComment,
        value: review.productRating,
        createdAt: review.createdAt
    };
}

async function getVendorProducts(ctx) {
    const { Product, State } = mongoose.models;
    const { store, user, retailer, states } = ctx.state;
    const payload = ctx.request.query;
    const shouldCheckTHC = !!(user && retailer);

    let showThc = false;
    let stateInfo = null;

    if (user && retailer) {
        // For authenticated users, check their showThc setting and state
        showThc = retailer.showThc;
        stateInfo = states ? states.find(st => st.id.toString() === retailer._state.toString()) : null;
    } else {
        // For public users, implement new THC visibility rules
        showThc = true;

        // Get the store's state information to determine filtering rules
        const storeState = await State.findById(store._state);
        stateInfo = storeState;

        // Note: For public users, we show all THC products for now since we don't have
        // visitor location detection implemented. This allows public viewing of all products
        // but purchase restrictions will still apply during cart/checkout for authenticated users.
    }

    const { query, sort } = await parseFilters(payload, store._state, showThc, stateInfo, shouldCheckTHC);
    query._vendor = store._id;


    const response = await Bluebird.props({
        list: Product
            .find(query)
            .sort(sort)
            .paginate(payload.page, payload.perPage)
            .populate('_vendor')
            .exec(),

        count: Product.count(query)
    });
    const filters = { ...payload };
    delete filters.page;
    delete filters.perPage;

    ctx.body = {
        data: response.list.map(product => product.formatPublic()),
        pagination: {
            total: response.count,
            page: payload.page,
            perPage: payload.perPage
        },
        filters
    };
}

async function getVendorProduct(ctx) {
    const { Review } = mongoose.models;
    const { product } = ctx.state;

    const bestReviewList = await Review.find({
        _product: product._id,
        deletedAt: { $exists: false },
        $and: [
            { productComment: { $exists: true } },
            { productComment: { $ne: '' } }
        ],
        productRating: { $in: [4, 5] }
    })
        .limit(3)
        .populate('_retailer');
    const reviewList = await Review.find({
        _product: product._id,
        deletedAt: { $exists: false },
        productComment: { $exists: true }
    })
        .limit(6)
        .populate('_retailer');
    const vendorReviewList = await Review.find({
        _vendor: product._vendor._id,
        deletedAt: { $exists: false },
        vendorComment: { $exists: true }
    })
        .limit(6)
        .populate('_retailer');

    const formattedProduct = product.formatPublic();

    formattedProduct.bestReviews = bestReviewList.map(formatReview);
    formattedProduct.reviews = reviewList.map(formatReview);
    formattedProduct.vendorReviews = vendorReviewList.map(formatReview);

    ctx.body = {
        data: formattedProduct
    };
}
