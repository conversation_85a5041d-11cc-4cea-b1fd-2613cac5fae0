'use strict';

const config = require('../../services/config');
const mongoose = require('mongoose');
const ObjectId = mongoose.Types.ObjectId;
const Bluebird = require('bluebird');
const { HttpError } = require('../../services/error');
const escapeRegex = require('escape-string-regexp');
const constants = require('../../models/constants');
const { DateTime } = require('luxon');
const mw = require('../../services/middleware');
const Router = require('koa-router');

module.exports = Router()
    .prefix('/products')
    .param('productKey', paramProduct)
    .get('/', getProducts)
    .get('/:productKey', getProduct);

async function paramProduct(productKey, ctx, next) {
    const { Product } = mongoose.models;
    const query = {
        key: productKey,
        deletedAt: { $exists: false },
        $or: [{ cannabisLicense: false }]
    };

    // New THC filtering logic: domestic users see THC from same state, international users see all THC
    if (ctx.state.retailer.showThc) {
        const { states } = ctx.state;
        const retailerState = states.find(st => st.id.toString() === ctx.state.retailer._state.toString());

        if (retailerState && !retailerState.isDomestic) {
            // International users can see all THC products
            query.$or.push({
                cannabisLicense: true
            });
        } else {
            // Domestic users can only see THC products from their state
            query.$or.push({
                cannabisLicense: true,
                _state: ctx.state.retailer._state
            });
        }
    }

    ctx.state.product = await Product.findOne(query).populate('_vendor _category');

    if (!ctx.state.product) {
        throw new HttpError(404);
    }

    await next();
}

async function getProducts(ctx) {
    const { Product } = mongoose.models;
    const { retailer, states } = ctx.state;
    const payload = ctx.request.query;
    const { _state } = retailer;
    let state = states.find(st => st.id.toString() === _state.toString());

    payload.deletedAt = { $exists: false };

    const { query, sort } = await parseFilters(payload, retailer._state, retailer.showThc, state);

    // console.log('what is the retailer???', retailer);
    // console.log('query', query)
    // const { _state } = retailer;
    // console.log('what is this?', _state)
    // // console.log('what are the sstates', states)

    // let state = states.find(st => st.id.toString() === _state.toString());
    // console.log('user state', state);

    try {
        const response = await Bluebird.props({
            list: Product
                .find(query)
                // .sort(sort)
                .paginate(payload.page, payload.perPage)
                .populate('_vendor')
                .exec(),

            count: Product.count(query)
        });
        // console.log('what is the response??', response)

        const filters = { ...payload };
        delete filters.page;
        delete filters.perPage;

        Product.incrementSearchViews(response.list.map(product => product._id));

        response.list.forEach(product => {
            if (product._vendor.isBroker) {
                product.files = [];
                product.batches.forEach(batch => {
                    batch.coa = [];
                    // Variants should contain COA?
                    // batch.variants.forEach(variant => {
                    //     variant.coa = [];
                    // });
                });
            }
        });

        ctx.body = {
            data: response.list.map(product => product.formatPublic()),
            pagination: {
                total: response.count,
                page: payload.page,
                perPage: payload.perPage
            },
            filters
        };
    } catch (err) {
        console.log('There was an error getting the products', err);
    }
}

// eslint-disable-next-line
async function parseFilters(payload, _state, showThc, stateFull, shouldCheckTHC = true) {
    const { Category } = mongoose.models;

    const thc_categories = await Category.find({
        parent: {
            $exists: true,
            $eq: 'ca0000000000000000000001'
        }
    });
    const thc_category_ids_array = thc_categories.map(category => category._id);    
    const query = {
        status: constants.PRODUCT.STATUS.ACTIVE,
        deletedAt: { $exists: false },
        $or: [{ cannabisLicense: false }],
        $expr: {
            $and: [
                {
                    $gt: [
                        {
                            $size: {
                                $filter: {
                                    input: "$batches",
                                    as: "batch",
                                    cond: {
                                        $gt: [
                                            {
                                                $size: {
                                                    $filter: {
                                                        input: "$$batch.variants",
                                                        as: "variant",
                                                        cond: {
                                                            $or: [
                                                                { $eq: ["$$variant.showProductNoInventory", true] },
                                                                {
                                                                    $and: [
                                                                        { $eq: ["$$variant.showProductNoInventory", false] },
                                                                        { $gt: ["$$variant.quantity", 0] }
                                                                    ]
                                                                }
                                                            ]
                                                        }
                                                    }
                                                }
                                            },
                                            0
                                        ]
                                    }
                                }
                            }
                        },
                        0
                    ]
                }
            ]
        }
    };


    // Apply new THC filtering logic based on user type and state
    if (shouldCheckTHC) {
        if (showThc && stateFull && !stateFull.isDomestic) {
            // International users can see all THC products
            query.$or.push({
                cannabisLicense: true
            });
        }
        else if (showThc) {
            // Domestic users can only see THC products from their state
            query.$or.push({
                cannabisLicense: true,
                _state
            });
        }
    } else {
        // For public users or when THC checking is disabled, show all THC products
        query.$or.push({
            cannabisLicense: true
        });
    }

    let sort;
    switch (payload.sort) {
        case 'review':
            sort = '-rating.value';
            break;
        case 'name':
            sort = 'name';
            break;
        case 'newest':
            sort = '-createdAt';
            break;
        case 'lowPrice':
            sort = 'price';
            break;
        case 'highPrice':
            sort = '-price';
            break;
        case 'popular':
        default:
            sort = '-_ranking.total';
    }

    if (payload.location) {
        query._state = payload.location;
    }

    if (payload.type) {
        query.type = payload.type;
    }

    if (payload.vendor) {
        query._vendor = payload.vendor;
    }

    if (payload.search) {
        query.name = { $regex: new RegExp(escapeRegex(payload.search), 'i') };
    }

    // TODO: Add preeviously viewed and viewed by other similar
    if (payload.category === 'justforyou') {
        // magic
    }
    else if (payload.category && ObjectId.isValid(payload.category)) {
        const categories = await Category.find({
            cannabisLicense: showThc ? { $in: [true, false] } : false,
            $or: [
                { _id: new ObjectId(payload.category) },
                { parent: new ObjectId(payload.category) }
            ]
        });
        if (categories.length) {
            query._category = { $in: categories };
        }
    }

    if (payload.strains && !Array.isArray(payload.strains)) {
        payload.strains = [payload.strains];
    }
    if (payload.strains && payload.strains.length) {
        query.strain = { $in: payload.strains };
    }
    if (payload.effects && !Array.isArray(payload.effects)) {
        payload.effects = [payload.effects];
    }
    if (payload.effects && payload.effects.length) {
        for (let effect of payload.effects) {
            query['effects.' + effect] = { $gt: 0 };
        }
    }
    if (payload.medicalSymptoms && !Array.isArray(payload.medicalSymptoms)) {
        payload.medicalSymptoms = [payload.medicalSymptoms];
    }
    if (payload.medicalSymptoms && payload.medicalSymptoms.length) {
        query.medicalSymptoms = { $in: payload.medicalSymptoms };
    }
    if (payload.medicalConditions && !Array.isArray(payload.medicalConditions)) {
        payload.medicalConditions = [payload.medicalConditions];
    }
    if (payload.medicalConditions && payload.medicalConditions.length) {
        query.medicalConditions = { $in: payload.medicalConditions };
    }
    if (payload.flower && !Array.isArray(payload.flower)) {
        payload.flower = [payload.flower];
    }
    if (payload.flower && payload.flower.length) {
        query.flower = { $in: payload.flower };
    }
    if (payload.environment && !Array.isArray(payload.environment)) {
        payload.environment = [payload.environment];
    }
    if (payload.environment && payload.environment.length) {
        query.environment = { $in: payload.environment };
    }

    if (payload.minThc) {
        if (!query['content.thc']) {
            query['content.thc'] = {};
        }
        query['content.thc'].$gte = payload.minThc;
    }
    if (payload.maxThc) {
        if (!query['content.thc']) {
            query['content.thc'] = {};
        }
        query['content.thc'].$lte = payload.maxThc;
    }
    if (payload.minCbd) {
        if (!query['content.cbd']) {
            query['content.cbd'] = {};
        }
        query['content.cbd'].$gte = payload.minCbd;
    }
    if (payload.maxCbd) {
        if (!query['content.cbd']) {
            query['content.cbd'] = {};
        }
        query['content.cbd'].$lte = payload.maxCbd;
    }
    if (payload.minCbn) {
        if (!query['content.cbn']) {
            query['content.cbn'] = {};
        }
        query['content.cbn'].$gte = payload.minCbn;
    }
    if (payload.maxCbn) {
        if (!query['content.cbn']) {
            query['content.cbn'] = {};
        }
        query['content.cbn'].$lte = payload.maxCbn;
    }

    if (payload.minPrice) {
        if (!query.price) {
            query.price = {};
        }
        query.price.$gte = payload.minPrice * 100;
    }
    if (payload.maxPrice) {
        if (!query.price) {
            query.price = {};
        }
        query.price.$lte = payload.maxPrice * 100;
    }
    return {
        query,
        sort
    };
}

async function metaFilters(query) {
    const { Product } = mongoose.models;
    const meta = await Product.aggregate([
        {
            $match: {
                status: query.status,
                $or: query.$or
            }
        },
        {
            $group: {
                _id: null,
                maxPrice: { $max: '$price' }
            }
        }
    ]);
    return meta && meta[0] || {};
}

async function getProduct(ctx) {
    const { Review } = mongoose.models;
    const { product } = ctx.state;

    const bestReviewList = await Review.find({
        _product: product._id,
        deletedAt: { $exists: false },
        $and: [
            { productComment: { $exists: true } },
            { productComment: { $ne: '' } }
        ],
        productRating: { $in: [4, 5] }
    })
        .limit(3)
        .populate('_retailer');
    const reviewList = await Review.find({
        _product: product._id,
        deletedAt: { $exists: false },
        productComment: { $exists: true }
    })
        .limit(6)
        .populate('_retailer');
    const vendorReviewList = await Review.find({
        _vendor: product._vendor._id,
        deletedAt: { $exists: false },
        vendorComment: { $exists: true }
    })
        .limit(6)
        .populate('_retailer');

    const formattedProduct = product.formatPublic();

    formattedProduct.bestReviews = bestReviewList.map(formatReview);
    formattedProduct.reviews = reviewList.map(formatReview);
    formattedProduct.vendorReviews = vendorReviewList.map(formatReview);

    // Handle the case in which the vendor is broker account
    if (formattedProduct.vendor.isBroker) {
        formattedProduct.files = [];

        formattedProduct.batches.forEach(batch => {
            batch.coa = [];
            // Variants should contain COA?
            // batch.variants.forEach(variant => {
            //     variant.coa = [];
            // });
        });
    }

    product.incrementProductViews();
    ctx.body = {
        data: formattedProduct
    };
}

function formatReview(review) {
    return {
        from: review._retailer && review._retailer.businessName || review.reviewerName,
        text: review.productComment,
        value: review.productRating,
        createdAt: review.createdAt
    };
}

module.exports.parseFilters = parseFilters;
