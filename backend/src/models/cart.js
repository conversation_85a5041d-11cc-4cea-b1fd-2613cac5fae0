'use strict';

const mongoose = require('mongoose');
const config = require('../services/config');
const { VariantSchema, eqVariants, getVariantFromProduct } = require('../services/util');
const constants = require('./constants');
const { ObjectId } = mongoose.Schema.Types;

// Helper function to validate if a retailer can purchase a THC product
async function canPurchaseThcProduct(product, retailer) {
    if (!product.cannabisLicense) {
        return true; // Non-THC products can be purchased by anyone
    }

    // Ensure retailer state is populated
    if (!retailer._state.isDomestic) {
        await retailer.populate('_state').execPopulate();
    }

    const retailerState = retailer._state;

    if (retailerState && retailerState.isDomestic) {
        // Domestic users can only purchase THC products from their state
        return product._state.equals(retailer._state);
    }

    // International users can purchase all THC products
    return true;
}

const CartItem = {
    _id: false,
    _product: {
        type: ObjectId,
        ref: 'Product',
        index: true
    },
    variant: VariantSchema,
    quantity: Number
};
const CartSchema = new mongoose.Schema(
    {
        _retailer: {
            type: ObjectId,
            ref: 'Retailer',
            index: true
        },
        items: {
            type: [CartItem],
            default: []
        }
    },
    { timestamps: true, name: 'Cart' }
);

CartSchema
    .index({ updatedAt: 1 }, { expireAfterSeconds: 60 * 60 * 24 * 7 })
    .static('findOrCreate', findOrCreate)
    .method('addProduct', addProduct)
    .method('updateProduct', updateProduct)
    .method('removeProduct', removeProduct)
    .method('emptyCart', emptyCart)
    .method('checkAndFormatPublic', checkAndFormatPublic);

const Cart = mongoose.model('Cart', CartSchema);

async function findOrCreate(retailerId) {
    let cart = await Cart.findOne({ _retailer: retailerId });
    if (!cart) {
        cart = new Cart({
            _retailer: retailerId,
            items: []
        });
        await cart.save();
    }
    return cart;
}

async function addProduct(product, variant, quantity) {
    const items = this.items;
    const existsItem = items.find(item => item._product.equals(product._id) && eqVariants(item.variant, variant));
    if (existsItem) {
        existsItem.quantity += quantity;
        if (existsItem.quantity > variant.quantity) {
            existsItem.quantity = variant.quantity;
        }
    }
    else {
        items.push({
            _product: product._id,
            variant,
            quantity
        });
    }
    this.set({ items });
    await this.save();
    product.incrementProductBuyClicks();
}

async function updateProduct(product, variant, quantity, index) {
    const items = this.items;
    const existsItem = items[index] || items.find(item => item._product.equals(product._id) && eqVariants(item.variant, variant));
    if (existsItem) {
        existsItem.variant = variant;
        existsItem.quantity = quantity;
        if (existsItem.quantity > variant.quantity) {
            existsItem.quantity = variant.quantity;
        }
    }
    else {
        items.push({
            _product: product._id,
            variant,
            quantity
        });
    }
    this.set({ items });
    await this.save();
}
async function removeProduct(product, variant) {
    const items = this.items.filter(item => !item._product.equals(product._id) || !eqVariants(item.variant, variant));
    this.set({ items });
    await this.save();
}

async function emptyCart() {    
    this.set({ items: []});
    await this.save();
}

async function checkAndFormatPublic() {
    const { Retailer } = mongoose.models;
    await this.populate('items._product').execPopulate();
    const retailer = await Retailer.findById(this._retailer._id);

    this.items = this.items
        .filter(item => {
            const product = item._product;
            if (!product || product.status !== constants.PRODUCT.STATUS.ACTIVE) {
                return false;
            }
            // Implement THC purchase eligibility validation
            if (product.cannabisLicense) {
                // Check if retailer's state is domestic
                const retailerState = retailer._state;
                if (retailerState && retailerState.isDomestic) {
                    // Domestic users can only purchase THC products from their state
                    if (!product._state.equals(retailer._state)) {
                        return false;
                    }
                }
                // International users can purchase all THC products (no restrictions)
            }
            let existProductVariant = getVariantFromProduct(product, item.variant);

            if (!existProductVariant) {
                return false;
            }
            if (item.variant.quantity < item.quantity) {
                return false;
            }
            return true;
        });

    await this.save();
    await this.populate('items._product._vendor').execPopulate();
    return this.items.map(item => ({
        quantity: item.quantity,
        variant: item.variant.formatPublic(),
        product: item._product.formatPublic()
    }));
}

module.exports = { canPurchaseThcProduct };
