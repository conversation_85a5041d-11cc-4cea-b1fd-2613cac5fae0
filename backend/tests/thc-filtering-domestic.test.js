'use strict';

const mongoose = require('mongoose');
const { expect } = require('chai');
const request = require('supertest');
const app = require('../src/app');
const { createTestUser, createTestProducts, cleanupTestData } = require('./helpers/test-helpers');

describe('THC Filtering - Domestic Accounts', function() {
    this.timeout(10000);
    
    let domesticRetailer, domesticUser, authToken;
    let californiaProducts, texasProducts, nonThcProducts;
    let californiaState, texasState;

    before(async function() {
        // Create test states
        const { State } = mongoose.models;
        californiaState = await State.findOneAndUpdate(
            { name: 'California' },
            { name: 'California', isDomestic: true },
            { upsert: true, new: true }
        );
        texasState = await State.findOneAndUpdate(
            { name: 'Texas' },
            { name: 'Texas', isDomestic: true },
            { upsert: true, new: true }
        );

        // Create domestic retailer in California
        const testData = await createTestUser({
            email: '<EMAIL>',
            state: californiaState._id,
            isDomestic: true,
            showThc: true
        });
        domesticRetailer = testData.retailer;
        domesticUser = testData.user;
        authToken = testData.token;

        // Create test products
        californiaProducts = await createTestProducts([
            {
                name: 'California THC Product 1',
                type: 'thc',
                cannabisLicense: true,
                _state: californiaState._id
            },
            {
                name: 'California THC Product 2', 
                type: 'thc',
                cannabisLicense: true,
                _state: californiaState._id
            }
        ]);

        texasProducts = await createTestProducts([
            {
                name: 'Texas THC Product 1',
                type: 'thc', 
                cannabisLicense: true,
                _state: texasState._id
            }
        ]);

        nonThcProducts = await createTestProducts([
            {
                name: 'CBD Product 1',
                type: 'hemp_derived_cbd',
                cannabisLicense: false,
                _state: californiaState._id
            },
            {
                name: 'Ancillary Product 1',
                type: 'ancillary',
                cannabisLicense: false,
                _state: texasState._id
            }
        ]);
    });

    after(async function() {
        await cleanupTestData();
    });

    describe('Product Visibility', function() {
        it('should show non-THC products from all states', async function() {
            const response = await request(app)
                .get('/retailer/products')
                .set('Authorization', `Bearer ${authToken}`)
                .query({ type: 'hemp_derived_cbd' })
                .expect(200);

            expect(response.body.data).to.be.an('array');
            const productNames = response.body.data.map(p => p.name);
            expect(productNames).to.include('CBD Product 1');
        });

        it('should show THC products only from same state (California)', async function() {
            const response = await request(app)
                .get('/retailer/products')
                .set('Authorization', `Bearer ${authToken}`)
                .query({ type: 'thc' })
                .expect(200);

            expect(response.body.data).to.be.an('array');
            const productNames = response.body.data.map(p => p.name);
            
            // Should include California THC products
            expect(productNames).to.include('California THC Product 1');
            expect(productNames).to.include('California THC Product 2');
            
            // Should NOT include Texas THC products
            expect(productNames).to.not.include('Texas THC Product 1');
        });

        it('should show all product types when no filter is applied', async function() {
            const response = await request(app)
                .get('/retailer/products')
                .set('Authorization', `Bearer ${authToken}`)
                .expect(200);

            expect(response.body.data).to.be.an('array');
            const productNames = response.body.data.map(p => p.name);
            
            // Should include non-THC products
            expect(productNames).to.include('CBD Product 1');
            expect(productNames).to.include('Ancillary Product 1');
            
            // Should include same-state THC products
            expect(productNames).to.include('California THC Product 1');
            
            // Should NOT include out-of-state THC products
            expect(productNames).to.not.include('Texas THC Product 1');
        });
    });

    describe('Individual Product Access', function() {
        it('should allow access to same-state THC products', async function() {
            const californiaProduct = californiaProducts[0];
            const response = await request(app)
                .get(`/retailer/products/${californiaProduct.key}`)
                .set('Authorization', `Bearer ${authToken}`)
                .expect(200);

            expect(response.body.data.name).to.equal('California THC Product 1');
            expect(response.body.data.cannabisLicense).to.be.true;
        });

        it('should deny access to out-of-state THC products', async function() {
            const texasProduct = texasProducts[0];
            await request(app)
                .get(`/retailer/products/${texasProduct.key}`)
                .set('Authorization', `Bearer ${authToken}`)
                .expect(404); // Should not be found due to filtering
        });

        it('should allow access to non-THC products from any state', async function() {
            const nonThcProduct = nonThcProducts[1]; // Ancillary from Texas
            const response = await request(app)
                .get(`/retailer/products/${nonThcProduct.key}`)
                .set('Authorization', `Bearer ${authToken}`)
                .expect(200);

            expect(response.body.data.name).to.equal('Ancillary Product 1');
            expect(response.body.data.cannabisLicense).to.be.false;
        });
    });

    describe('Category Filtering', function() {
        it('should include THC categories for domestic users', async function() {
            const response = await request(app)
                .get('/retailer/categories')
                .set('Authorization', `Bearer ${authToken}`)
                .expect(200);

            expect(response.body.data).to.be.an('array');
            // Should include THC categories since showThc is true
            const thcCategories = response.body.data.filter(cat => cat.cannabisLicense === true);
            expect(thcCategories.length).to.be.greaterThan(0);
        });
    });
});

console.log('Domestic Account THC Filtering Test Suite Created');
console.log('Run with: npm test -- --grep "THC Filtering - Domestic Accounts"');
