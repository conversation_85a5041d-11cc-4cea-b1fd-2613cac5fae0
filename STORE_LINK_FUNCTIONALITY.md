# Store Link Functionality Documentation

## Overview

This document describes the updated public store access functionality and how THC products are now visible to public users based on location and store state.

## Public Store Access

### Current Implementation

Public users (non-authenticated visitors) can now access vendor store links and view products without requiring authentication. The system implements the following visibility rules:

#### THC Product Visibility for Public Users

1. **All THC Products Visible**: Public users can view all THC products regardless of state restrictions
2. **No Purchase Capability**: Public users see "Sign Up to Purchase" buttons instead of "Add to Cart" buttons
3. **Full Product Information**: Public users can view all product details, images, and information

#### Authentication Flow

- When public users attempt to purchase products, they are redirected to the signup page
- The redirect URL preserves the current page location for seamless return after authentication
- Authenticated users are subject to state-based purchase restrictions

## Backend Implementation

### Store Controller (`backend/src/controllers/retailer/store.js`)

#### `getVendorProducts` Function
- **Public Users**: Shows all products including THC (no state restrictions for viewing)
- **Authenticated Users**: Applies state-based filtering based on user's domestic/international status

#### `paramProduct` Function
- Allows public access to individual THC product pages
- No authentication required for product viewing

#### `getVendorStore` Function
- Shows THC categories to public users by passing `showThc: true` to `filterCategories`

### Middleware (`backend/src/controllers/retailer/middleware.js`)

#### `retailerSpa` Function
- Sets `fakeUser: true` for public users accessing store pages
- Provides THC categories to public users for navigation

#### `filterCategories` Function
- When `showThc: true`, displays THC categories based on state rules
- For public users, shows all THC categories without restrictions

## Frontend Implementation

### Store Page (`frontend/src/retailer/components/StorePage.vue`)

- Handles both authenticated and public user experiences
- Redirects public users to signup when attempting to navigate away from store pages
- Preserves store context during authentication flow

### Product Card (`frontend/src/retailer/components/product/RichProductCard.vue`)

#### Public User Experience
- Shows "Sign Up to Purchase" button instead of cart buttons
- Displays purchase restriction notifications for THC products
- Allows viewing of all product information without authentication

#### Authenticated User Experience
- Shows standard "Add to Cart" and "Buy Now" buttons
- Applies state-based purchase restrictions
- Validates purchase eligibility before adding to cart

## Purchase Restrictions

### State-Based Restrictions

1. **Domestic Users**: Can only purchase THC products from their same state
2. **International Users**: Can purchase all THC products regardless of state
3. **Public Users**: Cannot purchase any products (must sign up first)

### Validation Points

1. **Add to Cart**: Validates THC purchase eligibility before adding products
2. **Cart Checkout**: Removes/warns about restricted products during checkout
3. **Order Creation**: Final validation before order placement

## COA (Certificate of Analysis) Requirements

### Vendor Warnings

- Vendors see COA warning banners when THC products lack COA documents
- Warning appears on all vendor pages (VendorModule, ProductPage, ProductListPage)
- Warning can be dismissed per session but reappears on page refresh

### Product Visibility

- THC products are visible to buyers even without COA documents
- COA requirements removed from product filtering logic
- Vendors still encouraged to upload COA documents for compliance

## API Endpoints

### Public Endpoints (No Authentication Required)

- `GET /retailer/store/:storeName` - Get vendor store information
- `GET /retailer/store/:storeName/products` - Get vendor products
- `GET /retailer/store/:storeName/products/:productKey` - Get individual product

### Authenticated Endpoints

- `GET /vendor/coa-warning` - Get COA warning status for vendor
- `POST /retailer/cart/` - Add product to cart (with purchase validation)
- `GET /retailer/cart/restrictions` - Check cart restrictions

## Testing

### Verified Functionality

1. ✅ Public store access without authentication
2. ✅ THC product visibility for public users
3. ✅ COA warning system for vendors
4. ✅ Purchase restriction validation
5. ✅ Cart validation for state restrictions

### Test Script

A test script (`test-thc-filtering.js`) is available to verify:
- Database connectivity and model integrity
- API endpoint availability
- Basic functionality of key features

## Future Enhancements

### Planned Features

1. **IP Geolocation**: Detect visitor location to apply state-specific THC visibility
2. **User Location Selection**: Allow users to specify their location for better filtering
3. **Enhanced Analytics**: Track public user behavior and conversion rates

### Considerations

- Current implementation shows all THC products to public users for maximum visibility
- Purchase restrictions are enforced at cart/checkout level for authenticated users
- Location detection would require additional privacy considerations and user consent

## Configuration

### Environment Variables

- `NODE_ENV`: Determines test vs production behavior
- Database connection settings in `config/database.js`
- App URL configuration in `config/app.js`

### State Configuration

States are configured with `isDomestic` flag to determine filtering rules:
- `isDomestic: true` - Domestic states (restricted THC access)
- `isDomestic: false` - International states (unrestricted THC access)

## Troubleshooting

### Common Issues

1. **Store Not Found (404)**: Verify store slug exists and is active
2. **Products Not Visible**: Check product status and vendor application status
3. **COA Warnings Not Showing**: Verify vendor has THC products without COA documents

### Debug Information

- Check browser console for JavaScript errors
- Verify API responses in Network tab
- Check server logs for backend errors
- Ensure database connectivity and model loading
