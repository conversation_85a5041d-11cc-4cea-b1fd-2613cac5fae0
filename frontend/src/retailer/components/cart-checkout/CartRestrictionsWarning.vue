<template>
  <div v-if="hasRestrictions" class="cart-restrictions-warning mb-4">
    <div class="alert alert-warning" role="alert">
      <i class="fa fa-exclamation-triangle mr-2"></i>
      <strong>Purchase Restrictions:</strong>
      <div class="mt-2">
        <div v-if="removedThcProducts.length > 0" class="mb-2">
          <div class="mb-1">
            The following THC products were removed from your cart because they cannot be shipped to your state:
          </div>
          <ul class="mb-0 pl-4">
            <li v-for="product in removedThcProducts" :key="product.key" class="small">
              {{ product.name }} - Available only in {{ product.stateName }}
            </li>
          </ul>
        </div>
        <div v-if="expiredLicense" class="mb-2">
          <div class="text-danger">
            <strong>License Expired:</strong> Your cannabis license has expired. 
            Please renew your license to purchase THC products.
          </div>
        </div>
        <div v-if="missingLicense" class="mb-2">
          <div class="text-info">
            <strong>License Required:</strong> A valid cannabis license is required to purchase THC products.
            Please complete your license application.
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapGetters, mapState } from 'vuex';

export default {
  name: 'CartRestrictionsWarning',
  data() {
    return {
      removedThcProducts: [],
      expiredLicense: false,
      missingLicense: false
    };
  },
  computed: {
    ...mapGetters(['retailer', 'state']),
    ...mapState(['cart']),
    hasRestrictions() {
      return this.removedThcProducts.length > 0 || this.expiredLicense || this.missingLicense;
    }
  },
  methods: {
    async checkCartRestrictions() {
      try {
        // This would be called when cart is updated to check for removed products
        // For now, we'll check if there are any THC products that might be restricted
        const response = await this.$http.get('/cart/restrictions');
        const { removedProducts, licenseStatus } = response.data;
        
        this.removedThcProducts = removedProducts || [];
        this.expiredLicense = licenseStatus === 'expired';
        this.missingLicense = licenseStatus === 'missing';
      } catch (error) {
        // If endpoint doesn't exist yet, we can still show basic warnings
        console.log('Cart restrictions check not available yet');
      }
    }
  },
  mounted() {
    this.checkCartRestrictions();
    
    // Listen for cart updates to recheck restrictions
    this.$root.$on('cart:updated', this.checkCartRestrictions);
  },
  beforeDestroy() {
    this.$root.$off('cart:updated', this.checkCartRestrictions);
  }
};
</script>

<style scoped>
.cart-restrictions-warning .alert {
  border-left: 4px solid #f0ad4e;
}

.cart-restrictions-warning .alert-warning {
  background-color: #fcf8e3;
  border-color: #f0ad4e;
  color: #8a6d3b;
}

.cart-restrictions-warning ul {
  list-style-type: disc;
}

.cart-restrictions-warning .text-danger {
  color: #d9534f !important;
}

.cart-restrictions-warning .text-info {
  color: #5bc0de !important;
}
</style>
