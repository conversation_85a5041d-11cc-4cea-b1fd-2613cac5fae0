<template>
    <div class="d-table-cell" :class="{
        'text-middle': isEmptyCart,
        'text-top': !isEmptyCart
    }" v-qa="'cart-page'">
        <template>
            <!-- Empty cart -->
            <template v-if="isEmptyCart">
                <div class="container my-6">
                    <div class="mb-3">
                        <svg class="icon icon-5x d-block mx-auto text-muted" viewBox="0 0 24 24">
                            <path
                                d="M17.21 9L12.83 2.44A0.99 0.99 0 0 0 12 2.02C11.68 2.02 11.36 2.16 11.17 2.45L6.79 9H2C1.45 9 1 9.45 1 10 1 10.09 1.01 10.18 1.04 10.27L3.58 19.54C3.81 20.38 4.58 21 5.5 21H18.5C19.42 21 20.19 20.38 20.43 19.54L22.97 10.27 23 10C23 9.45 22.55 9 22 9H17.21ZM9 9L12 4.6 15 9H9ZM12 17C10.9 17 10 16.1 10 15S10.9 13 12 13 14 13.9 14 15 13.1 17 12 17Z"
                                fill-rule="evenOdd"></path>
                        </svg>
                    </div>
                    <h3 class="mt-0 mb-6 text-center text-300 text-size-32">
                        Your Cart is Empty
                    </h3>
                    <div class="text-center max-w-250 mx-auto">
                        <router-link to="/" class="btn btn-lg btn-info btn-block" active-class="">
                            Start Shopping
                        </router-link>
                    </div>
                </div>
            </template>
            <!-- Not empty cart -->
            <template v-else>
                <div class="container my-3 my-sm-6">
                    <div class="d-flex d-xs-block align-items-center mb-6">
                        <div>
                            <h3 class="mt-0 mb-0 text-400">
                                Cart
                            </h3>
                        </div>
                        <div class="mt-3 visible-xs-block"></div>
                        <div class="ml-auto">
                            <router-link to="/" active-class="">
                                Continue Shopping<svg class="icon" viewBox="0 0 24 24">
                                    <path d="M8.59 16.34L13.17 11.75 8.59 7.16 10 5.75 16 11.75 10 17.75Z"
                                        fill-rule="evenodd"></path>
                                </svg>
                            </router-link>
                        </div>
                    </div>
                    <cart-restrictions-warning></cart-restrictions-warning>
                    <div>
                        <div class="row-condensed">
                            <div class="col-sm-8">
                                <div class="panel panel-default mb-0">
                                    <div class="p-6">
                                        <div v-for="(item, index) in cart" :key="index">
                                            <hr class="hr-gray-7 my-6" v-if="index">
                                            <cart-product-card :cart-item="item"
                                                @change-quantity="changeQuantity(item, index, $event)"
                                                @change-variant="changeVariant(item, index, $event)"
                                                @remove="removeProductFromCart(item)"></cart-product-card>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="mt-3 visible-xs-block"></div>

                            <div class="col-sm-4">
                                <div class="panel panel-default mb-0 hide-overflow">
                                    <div class="p-6 bg-gray-2">
                                        <div>
                                            <div>
                                                <h4 class="mt-0 mb-2 text-gray-base text-500 text-uppercase text-size-20">
                                                    Order Summary
                                                </h4>

                                                <table class="w-100">
                                                    <tr v-if="0">
                                                        <td class="text-top pr-3">
                                                            <div class="opacity-50 text-size-14">
                                                                Ship to:
                                                            </div>
                                                        </td>
                                                        <td class="text-top text-right">
                                                            <template
                                                                v-if="retailer.lastDeliveryAddress && retailer.lastDeliveryAddress.name">

                                                                <div>
                                                                    {{ retailer.lastDeliveryAddress.line1 }}<template
                                                                        v-if="retailer.lastDeliveryAddress.line1 && retailer.lastDeliveryAddress.line2">,</template>
                                                                    {{ retailer.lastDeliveryAddress.line2 }}
                                                                </div>
                                                                <div>
                                                                    {{ retailer.lastDeliveryAddress.city }},
                                                                    <state :value="retailer.lastDeliveryAddress.state" />,
                                                                    {{ retailer.lastDeliveryAddress.postal }}
                                                                </div>
                                                                <div>
                                                                    United States
                                                                </div>
                                                                <div v-if="retailer.lastDeliveryAddress.phone">
                                                                    <img svg-inline class="icon"
                                                                        src="@images/inline-svg/icons/phone.svg" alt="">
                                                                    {{ retailer.lastDeliveryAddress.phone }}
                                                                </div>
                                                            </template>
                                                            <template v-else>
                                                                —
                                                            </template>
                                                        </td>
                                                    </tr>
                                                    <!--                                                    <template v-if="lastPaymentMethod.method && lastPaymentMethod.method !== 'creditCard' || lastCreditCard">-->
                                                    <template
                                                        v-if="lastPaymentMethod.method && lastPaymentMethod.method !== 'creditCard'">
                                                        <tr>
                                                            <td colspan="2" class="py-2"></td>
                                                        </tr>
                                                        <tr>
                                                            <td class="text-top pr-3">
                                                                <div class="opacity-50 text-size-14">
                                                                    Payment:
                                                                </div>
                                                            </td>
                                                            <td class="text-top text-right">
                                                                <div class="text-gray-base"
                                                                    v-if="lastPaymentMethod.method === 'check'">
                                                                    Mail a Check
                                                                </div>
                                                                <!--                                                                <div v-else-if="lastPaymentMethod.method === 'creditCard' && lastCreditCard">-->
                                                                <!--                                                                    <div class="text-gray-base">-->
                                                                <!--                                                                        {{lastCreditCard.brand}}-->
                                                                <!--                                                                    </div>-->
                                                                <!--                                                                    Ends with xxx­{{lastCreditCard.last4}}-->
                                                                <!--                                                                </div>-->
                                                                <div class="text-gray-base" v-else>
                                                                    Wire Transfer
                                                                </div>
                                                            </td>
                                                        </tr>
                                                    </template>
                                                    <tr>
                                                        <td colspan="2" class="py-2"></td>
                                                    </tr>
                                                    <tr>
                                                        <td class="text-top pr-3">
                                                            <div class="opacity-50 text-size-14">
                                                                Item Total:
                                                            </div>
                                                        </td>
                                                        <td class="text-top text-right">
                                                            <div class="text-gray-base">
                                                                {{ cartInfo.itemsPrice | price }}
                                                            </div>
                                                        </td>
                                                    </tr>
                                                    <tr>
                                                        <td colspan="2" class="py-2"></td>
                                                    </tr>
                                                    <tr>
                                                        <td class="text-top pr-3">
                                                            <div class="opacity-50 text-size-14">
                                                                Wholesale Discounts:
                                                            </div>
                                                        </td>
                                                        <td class="text-top text-right">
                                                            <div class="text-gray-base">
                                                                - {{ cartInfo.quantityDiscount | price }}
                                                            </div>
                                                        </td>
                                                    </tr>
                                                    <tr>
                                                        <td colspan="2" class="py-2"></td>
                                                    </tr>
                                                    <tr>
                                                        <td class="text-top pr-3">
                                                            <div class="opacity-50 text-size-14">
                                                                Estimated Delivery:
                                                            </div>
                                                        </td>
                                                        <td class="text-top text-right">
                                                            <div class="text-gray-base">
                                                                {{ cartInfo.deliveryPrice | price }}
                                                            </div>
                                                        </td>
                                                    </tr>
                                                    <!--                                                    <tr>-->
                                                    <!--                                                        <td colspan="2" class="py-2"></td>-->
                                                    <!--                                                    </tr>-->
                                                    <!--                                                    <tr>-->
                                                    <!--                                                        <td class="text-top pr-3">-->
                                                    <!--                                                            <div class="opacity-50 text-size-14">-->
                                                    <!--                                                                Payment Processing Fee:-->
                                                    <!--                                                            </div>-->
                                                    <!--                                                        </td>-->
                                                    <!--                                                        <td class="text-top text-right">-->
                                                    <!--                                                            <div class="text-gray-base">-->
                                                    <!--                                                                {{cartInfo.paymentFee | price}}-->
                                                    <!--                                                            </div>-->
                                                    <!--                                                        </td>-->
                                                    <!--                                                    </tr>-->
                                                </table>
                                            </div>
                                        </div>
                                    </div>
                                    <hr class="my-0 hr-gray-7">
                                    <div class="p-6">
                                        <div>
                                            <div>
                                                <div class="text-center">
                                                    <div class="text-gray-base text-500 mb-1">
                                                        Order Total
                                                    </div>
                                                    <div class="text-size-24 text-600 text-brand-primary mb-3">
                                                        {{ cartInfo.totalPrice | price }}
                                                    </div>
                                                    <div>
                                                        <button class="btn btn-lg btn-warning btn-block"
                                                            @click="checkAndNext()" v-qa="'placeOrder-btn'">
                                                            Place Order
                                                        </button>
                                                        <button class="btn btn-lg btn-info btn-block"
                                                            @click="continueShopping()" v-qa="'placeOrder-btn'">
                                                            Continue Shopping
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </template>
        </template>
    </div>
</template>

<script>
import { mapActions, mapGetters, mapState } from 'vuex';
import CartProductCard from './CartProductCard';
import CartRestrictionsWarning from './CartRestrictionsWarning';

export default {
    name: 'CartPage',
    components: {
        CartProductCard,
        CartRestrictionsWarning
    },
    data() {
        return {
        };
    },
    computed: {
        ...mapState(['retailer', 'order']),
        ...mapGetters(['cart', 'cartInfo', 'lastPaymentMethod']),
        isEmptyCart() {
            return !this.cart.length;
        },
        lastCreditCard() {
            return this.retailer.creditCards.find(card => card.id === this.lastPaymentMethod.id);
        }
    },
    methods: {
        ...mapActions(['changeCart', 'removeProductFromCart']),
        changeQuantity(item, index, quantity) {
            this.changeCart({
                index, cartItem: {
                    ...item,
                    quantity
                }
            });
        },
        changeVariant(item, index, variant) {
            this.changeCart({
                index, cartItem: {
                    ...item,
                    variant
                }
            });
        },
        checkAndNext() {
            this.$validator.validateAll()
                .then(result => {
                    if (!result) {
                        return;
                    }
                    return this.$router.push('/checkout');
                });
        },
        continueShopping() {
            this.$router.push('/search');
        }
    }
};
</script>
