<template>
    <div class="header-row">
        <div class="d-table-cell bg-white">
            <div class="header">
                <div class="container-fluid">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="d-flex align-items-center">

                                <!--menu icon (hamburger)-->
                                <div class="visible-sm-block visible-xs-block">
                                    <div class="d-flex align-items-center">
                                        <div class="text-gray-light cursor-pointer text-h3 mr-3"
                                             @click.prevent.stop="toggleSlideMenu()" v-qa="'hamburger-btn'">
                                            <svg class="icon d-block" viewBox="0 0 24 24">
                                                <path d="M3 18H21V16H3V18ZM3 13H21V11H3V13ZM3 6V8H21V6H3Z"
                                                      fill-rule="evenOdd"></path>
                                            </svg>
                                        </div>
                                    </div>
                                </div>

                                <!--logo-->
                                <div>
                                    <div class="my-6">
                                        <router-link to="/" class="text-white text-nodecor" v-qa="'logo'">
                                            <Logo @click="logoClick"/>
                                        </router-link>
                                    </div>
                                </div>
                                <div class="mr-3"></div>

                                <div class="dropdown mr-3 ml-minus-4">
                                    <div class="ml-4 text-retailer" v-qa="'currentRoleRetailer'"><b>Retailer</b></div>
                                </div>

                                <!--state-->
                                <div class="hidden-sm hidden-xs mr-6">
                                    <div class="p-rel">
                                        <div class="d-flex align-items-center cursor-pointer"
                                             @click.prevent="openGeoFencedModal()" v-qa="'geoFenced-btn'">
                                            <div class="p-rel w-em-6">
                                                <div class="p-abs centered">
                                                    <img src="@images/retailer/image-geo-fenced.svg" class="img-block"
                                                         alt="">
                                                </div>
                                            </div>
                                            <div>
                                                <div class="small">
                                                    <div v-qa="'stateName'">{{state.name}}</div>
                                                    <a class="cursor-pointer text-decor">Geo-fenced</a>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="hidden-xs flex-grow-1">
                            <div class="max-w-em-15 p-rel">
                                <div>
                                    <retailer-suggestions/>
                                </div>
                            </div>
                        </div>

                        <div class="ml-auto px-xs-3">
                            <div class="d-flex align-items-center cursor-pointer text-brand-info border-radius-3"
                                 @click="openHelpModal" v-qa="'helpModal-btn'">
                                <div class="text-size-27">
                                    <svg class="icon d-block mx-auto" viewBox="0 0 24 24">
                                        <path d="M11 18H13V16H11V18ZM12 2C6.48 2 2 6.48 2 12S6.48 22 12 22 22 17.52 22 12 17.52 2 12 2ZM12 20C7.59 20 4 16.41 4 12S7.59 4 12 4 20 7.59 20 12 16.41 20 12 20ZM12 6A4 4 0 0 0 8 10H10C10 8.9 10.9 8 12 8S14 8.9 14 10C14 12 11 11.75 11 15H13C13 12.75 16 12.5 16 10A4 4 0 0 0 12 6Z"
                                              fill-rule="evenodd"></path>
                                    </svg>
                                </div>
                                <div class="text-center text-700 text-nowrap text-size-27 ml-1 hidden-xs hidden-sm hidden-md">
                                    24/7 HELP
                                </div>
                            </div>
                        </div>

                        <!--desktop user menu-->
                        <div class="ml-auto hidden-xs hidden-sm min-w-0">
                            <div class="d-flex d-sm-block justify-content-end">


                                <!--Cart-->
                                <div class="px-sm-4 flex-shrink-0">
                                    <router-link to="/cart"
                                                 class="d-flex align-items-center text-brand-primary" v-qa="'cart-btn'">
                                        <span class="text-size-24">
                                            <span class="d-inline-block text-middle p-rel">
                                                <img svg-inline class="icon d-block"
                                                     src="@images/inline-svg/icons/cart.svg" alt="">
                                                <span class="header-retailer-menu-badge-icon"
                                                      v-qa="'cartCount'"
                                                      v-if="cartInfo.length">{{cartInfo.length}}</span>
                                            </span>
                                        </span>
                                    </router-link>
                                </div>
                                <template v-if="!fakeUser && user.key">
                                    <div class="vr hr-gray-7 align-self-stretch flex-shrink-0"></div>

                                    <!--Orders-->
                                    <div class="px-sm-4 flex-shrink-0">
                                        <router-link to="/orders"
                                                     class="d-flex align-items-center text-brand-primary"
                                                     v-qa="'orders-btn'">
                                            <span class="text-size-24">
                                                <span class="d-inline-block text-middle p-rel">
                                                    <img svg-inline class="icon d-block"
                                                         src="@images/inline-svg/icons/store.svg" alt="">
                                                    <span class="header-retailer-menu-badge-icon"
                                                          v-qa="'ordersCount'"
                                                          v-if="meta.orders">{{meta.orders}}</span>
                                                </span>
                                            </span>
                                        </router-link>
                                    </div>
                                    <div class="vr hr-gray-7 align-self-stretch flex-shrink-0"></div>

                                    <!--Tickets-->
                                    <div class="px-sm-4 flex-shrink-0">
                                        <router-link to="/tickets"
                                                     class="d-flex align-items-center text-brand-primary"
                                                     v-qa="'tickets-btn'">
                                            <span class="text-size-24">
                                                <span class="d-inline-block text-middle p-rel">
                                                    <img svg-inline class="icon d-block"
                                                         src="@images/inline-svg/icons/mail.svg" alt="">
                                                    <span class="header-retailer-menu-badge-icon"
                                                          v-qa="'ticketsCount'"
                                                          v-if="meta.tickets">{{meta.tickets}}</span>
                                                </span>
                                            </span>
                                        </router-link>
                                    </div>
                                    <div class="vr hr-gray-7 align-self-stretch flex-shrink-0"></div>

                                    <!--Settings-->
                                    <div class="px-sm-4 flex-shrink-0 dropdown"
                                         :class="{open: dropdowns['settings']}">
                                        <a @click.stop.prevent="toggleDropdowns('settings')"
                                           v-qa="'settings-btn'"
                                           class="d-flex align-items-center text-brand-primary cursor-pointer">
                                            <span class="text-size-24">
                                                <span class="d-inline-block text-middle">
                                                    <img svg-inline class="icon d-block"
                                                         src="@images/inline-svg/icons/cog.svg" alt="">
                                                </span>
                                            </span>
                                        </a>

                                        <ul class="dropdown-menu dropdown-menu-right mt-3"
                                            @click="toggleDropdowns('settings')">

                                            <!--Order History-->
                                            <router-link to="/orders" tag="li" v-qa="'orders-btn'">
                                                <a href="/retailer/orders">
                                                    Order History
                                                </a>
                                            </router-link>

                                            <!--Account Settings-->
                                            <router-link to="/settings" tag="li" exact v-qa="'userSettings-btn'">
                                                <a href="/retailer/settings">
                                                    Account Settings
                                                </a>
                                            </router-link>

                                            <!--Retailer Settings-->
                                            <router-link to="/settings/retailer" tag="li" exact
                                                         v-qa="'retailerSettings-btn'">
                                                <a href="/retailer/settings">
                                                    Retailer Settings
                                                </a>
                                            </router-link>

                                            <!--Team Settings-->
                                            <router-link to="/settings/team" tag="li" exact v-qa="'teamSettings-btn'">
                                                <a href="/retailer/settings/team">
                                                    Team Settings
                                                </a>
                                            </router-link>

                                            <!--Payment Methods-->
                                            <router-link to="/settings/payments" tag="li" v-qa="'paymentSettings-btn'">
                                                <a href="/retailer/payments">
                                                    Payment Methods
                                                </a>
                                            </router-link>

                                            <!--Delivery Addresses-->
                                            <router-link to="/settings/addresses" tag="li" v-qa="'addresses-btn'">
                                                <a href="/retailer/settings/addresses">
                                                    Delivery Addresses
                                                </a>
                                            </router-link>
                                            
                                            <li class="divider"></li>

                                            <!--Logout-->
                                            <li>
                                                <a class="cursor-pointer" @click="logout('retailer')"
                                                   v-qa="'logout-btn'">
                                                    <img svg-inline class="icon"
                                                         src="@images/inline-svg/icons/exit.svg"
                                                         alt="">
                                                    Logout
                                                </a>
                                            </li>
                                        </ul>
                                    </div>

                                    <div class="vr hr-gray-7 align-self-stretch flex-shrink-0"></div>

                                    <!--Accounts-->
                                    <div class="px-sm-4 dropdown max-w-em-15 min-w-0"
                                         v-if="availableRetailers.length || availableVendors.length"
                                         :class="{open: dropdowns['accounts']}">

                                        <a @click.stop.prevent="toggleDropdowns('accounts')"
                                           v-qa="'accounts-btn'"
                                           class="d-flex align-items-center text-brand-primary cursor-pointer min-w-0">
                                            <div class="min-w-0">
                                                <div class="text-truncate text-right font-weight-700 font-size-16 line-height-19">
                                                    {{retailer.businessName}}
                                                </div>
                                                <div class="text-truncate text-right font-size-13 line-height-15">
                                                    {{user.name}}
                                                </div>
                                            </div>
                                            <div class="ml-1">
                                                <span class="caret"></span>
                                            </div>
                                        </a>

                                        <ul class="dropdown-menu dropdown-menu-right mt-3"
                                            @click="toggleDropdowns('accounts')">

                                            <template>
                                                <li class="dropdown-header">Retailers:</li>
                                                <li class="active">
                                                    <a @click.prevent.stop
                                                       v-qa="'retailer-' + retailer.key">
                                                        <span class="font-size-15">{{retailer.businessName}}</span>
                                                    </a>
                                                </li>
                                                <li v-for="ret in availableRetailers" :key="ret.key">
                                                    <a class="cursor-pointer" @click="switchToAccountTo(ret)"
                                                       v-qa="'retailer-' + ret.key">
                                                        <span class="font-size-15">{{ret.businessName}}</span>
                                                    </a>
                                                </li>
                                            </template>
                                            <li class="divider" v-if="availableRetailers.length && availableVendors.length"></li>
                                            <template v-if="availableVendors.length">
                                                <li class="dropdown-header">Vendors:</li>
                                                <li v-for="vend in availableVendors" :key="vend.key">
                                                    <a class="cursor-pointer" @click="switchToAccountTo(vend)"
                                                       v-qa="'vendor-' + vend.key">
                                                        <span class="font-size-15">{{vend.storeName}}</span>
                                                    </a>
                                                </li>
                                            </template>
                                        </ul>


                                    </div>
                                </template>

                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!--mobile user menu-->
            <div class="hidden-md hidden-lg">
                <transition name="header-slide-menu">
                    <div class="header-slide-menu d-flex flex-column"
                         v-show="isSlideMenuOpen"
                         @click="closeSlideMenu()">
                        <div class="py-3">

                            <template v-if="availableRetailers.length || availableVendors.length">
                                <div class="px-4 text-truncate font-weight-700">{{retailer.businessName}}</div>
                                <div class="px-4 text-truncate font-weight-400 font-size-14 line-height-16 mb-3">
                                    {{user.name}}
                                </div>

                                <div class="mt-3">
                                    <div class="px-4 font-size-13 opacity-80 mb-1">Retailers:</div>
                                    <div>
                                        <hr class="my-0 opacity-20">
                                        <div class="px-4 py-3 bg-gray-3"
                                             v-qa="'retailer-' + retailer.key">
                                            <b class="font-size-15">{{retailer.businessName}}</b>
                                        </div>
                                    </div>
                                    <div v-for="ret in availableRetailers" :key="ret.key">
                                        <hr class="my-0 opacity-20">
                                        <a class="d-block text-primary px-4 py-3 text-nodecor cursor-pointer"
                                           @click="switchToAccountTo(ret)" v-qa="'retailer-' + ret.key">
                                            <span class="font-size-15">{{ret.businessName}}</span>
                                        </a>
                                    </div>
                                </div>
                                <div class="mt-3" v-if="availableVendors.length">
                                    <div class="px-4 font-size-13 opacity-80 mb-1">Vendors:</div>
                                    <div v-for="vend in availableVendors" :key="vend.key">
                                        <hr class="my-0 opacity-20">
                                        <a class="d-block text-primary px-4 py-3 text-nodecor cursor-pointer"
                                           @click="switchToAccountTo(vend)" v-qa="'vendor-' + vend.key">
                                            <span class="font-size-15">{{vend.storeName}}</span>
                                        </a>
                                    </div>
                                </div>

                                <hr class="mt-3 mb-0">
                            </template>

                            <!--Home-->
                            <router-link :to="{name: 'home'}" exact
                                         v-qa="'home-btn'"
                                         class="d-flex text-primary px-4 py-3 text-nodecor"
                                         active-class="bg-gray-3">
                                Home
                            </router-link>
                            <hr class="my-0 opacity-20">

                            <!--Shopping Cart-->
                            <router-link to="/cart"
                                         v-qa="'cart-btn'"
                                         class="d-flex text-primary px-4 py-3 text-nodecor justify-content-space-between"
                                         active-class="bg-gray-3">
                                <span class="min-w-0 text-truncate">
                                    Shopping Cart
                                </span>
                                <span class="flex-shrink-0">
                                    <span class="d-block px-1 border-radius-20 bg-warning text-white text"
                                          v-if="cartInfo.length">{{cartInfo.length}}</span>
                                </span>
                            </router-link>
                            <hr class="my-0 opacity-20">
                            <template v-if="!fakeUser && user.key">

                                <!--Tickets-->
                                <router-link to="/tickets"
                                             v-qa="'tickets-btn'"
                                             class="d-flex text-primary px-4 py-3 text-nodecor justify-content-space-between"
                                             active-class="bg-gray-3">
                                    <span class="min-w-0 text-truncate">
                                        Tickets
                                    </span>
                                    <span class="flex-shrink-0">
                                        <span class="d-block px-1 border-radius-20 bg-warning text-white text"
                                              v-if="meta.tickets">{{meta.tickets}}</span>
                                    </span>
                                </router-link>

                                <hr class="my-0 opacity-20">

                                <!--Account Settings-->
                                <router-link to="/settings"
                                             v-qa="'userSettings-btn'"
                                             class="d-block text-primary px-4 py-3 text-nodecor"
                                             active-class="bg-gray-3"
                                             exact>
                                    Account Settings
                                </router-link>
                                <hr class="my-0 opacity-20">

                                <router-link to="/settings/retailer"
                                             v-qa="'retailerSettings-btn'"
                                             class="d-block text-primary px-4 py-3 text-nodecor"
                                             active-class="bg-gray-3"
                                             exact>
                                    Retailer Settings
                                </router-link>
                                <hr class="my-0 opacity-20">

                                <!--Team Settings-->
                                <router-link to="/settings/team"
                                             v-qa="'teamSettings-btn'"
                                             class="d-block text-primary px-4 py-3 text-nodecor"
                                             active-class="bg-gray-3"
                                             exact>
                                    Team Settings
                                </router-link>
                                <hr class="my-0 opacity-20">

                                <!--Payment Methods-->
                                <router-link to="/settings/payments"
                                             v-qa="'paymentSettings-btn'"
                                             class="d-block text-primary px-4 py-3 text-nodecor"
                                             active-class="bg-gray-3">
                                    Payment Methods
                                </router-link>
                                <hr class="my-0 opacity-20">

                                <!--Delivery Addresses-->
                                <router-link to="/settings/addresses"
                                             v-qa="'addresses-btn'"
                                             class="d-block text-primary px-4 py-3 text-nodecor"
                                             active-class="bg-gray-3">
                                    Delivery Addresses
                                </router-link>
                                <hr class="my-0 opacity-20">

                                <!--Order History-->
                                <router-link to="/orders"
                                             v-qa="'orders-btn'"
                                             class="d-block text-primary px-4 py-3 text-nodecor"
                                             active-class="bg-gray-3">
                                    Order History
                                </router-link>
                            </template>
                        </div>

                        <!--Logout-->
                        <div class="mt-auto" v-if="user.key">
                            <div class="pb-3">

                                <hr class="my-0"/>

                                <a class="d-block text-primary px-4 py-3 text-nodecor cursor-pointer"
                                   v-qa="'logout-btn'"
                                   active-class="bg-gray-3"
                                   @click="logout('retailer')">
                                    <img svg-inline class="icon" src="@images/inline-svg/icons/exit.svg"
                                         alt="">
                                    Logout
                                </a>
                            </div>
                        </div>
                    </div>
                </transition>
                <transition name="header-slide-menu-overlay">
                    <div class="header-slide-menu-overlay"
                         v-show="isSlideMenuOpen"
                         @click="closeSlideMenu()"></div>
                </transition>
            </div>

            <div class="visible-xs-block pb-3 mt-minus-4">
                <div class="container">
                    <retailer-suggestions/>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import GeoFencedModal from '@/common/components/GeoFencedModal';
import HelpModal from '@/common/components/HelpModal.vue';
import Logo from '@/common/components/Logo';
import {mapActions, mapGetters, mapState} from 'vuex';
import RetailerSuggestions from './RetailerSuggestions';

export default {
    name: 'Header',
    inject: ['$retailerService'],
    components: {
        Logo,
        RetailerSuggestions
    },
    data() {
        return {
            dropdowns: {},
            isSlideMenuOpen: false
        };
    },
    beforeDestroy() {
        this.$root.$off('click');
        this.$root.$off('keydown');
    },
    watch: {
        dropdowns() {
            if (Object.keys(this.dropdowns).length) {
                document.addEventListener('click', this.closeDropdowns);
            }
        }
    },
    computed: {
        ...mapState(['retailer', 'meta']),
        ...mapGetters(['user', 'state', 'cartInfo', 'fakeUser', 'availableRetailers', 'availableVendors'])
    },
    methods: {
        ...mapActions(['logout']),
        switchToAccountTo(data) {
            this.$retailerService.switchAccount(data)
                .then(url => {
                    window.location.href = url;
                })
                .catch(err => {
                    this.$flashError(err);
                });
        },
        logoClick() {
            this.$root.$emit('filters:reset');
        },
        openGeoFencedModal() {
            this.$modal.open(GeoFencedModal, {size: 'modal-800'});
        },
        openHelpModal() {
            this.$modal.open(HelpModal, {
                size: 'modal-800',
                props: {
                    role: 'retailer'
                }
            });
        },
        toggleSlideMenu() {
            this.isSlideMenuOpen = !this.isSlideMenuOpen;
        },
        closeSlideMenu() {
            this.isSlideMenuOpen = false;
        },
        closeDropdowns() {
            this.dropdowns = {};
        },
        toggleDropdowns(name) {
            const state = this.dropdowns[name];

            this.dropdowns = {
                [name]: !state
            };
        }
    }
};
</script>
