<template>
    <div>
        <div class="p-rel">
            <div class="p-abs top-right z-index-1 pt-2 pr-2" @click="hide" aria-label="Close">
                <span class="cursor-pointer text-gray text-h4">
                    <img svg-inline class="icon d-block" src="@images/inline-svg/icons/close.svg" alt="">
                </span>
            </div>
        </div>
        <div class="p-3 p-sm-6">
            <div class="row-flex">
                <div class="col-flex col-sm-width-6 col-xs-width-12">
                    <images-carousel :product="product" :variant="variant" @select="variant = $event" />
                </div>
                <div class="col-flex col-sm-width-6 col-xs-width-12">
                    <div class="mt-6 visible-xs-block"></div>
                    <h3 class="mt-3 mb-1 text-gray-base" v-qa="'productPage-' + product.key">
                        {{ product.name }}
                        <template v-if="product.strain">
                            ({{ product.strain }})
                        </template>
                    </h3>
                    <!--stars-->
                    <rating class="mb-3 text-brand-warning text-h3" :rating="product.rating"></rating>
                    <!--price-->
                    <div class="mb-3">
                        <!-- TODO: Add discount price when coupon and product have one-to-one relationship  -->
                        <!-- <template v-if="discountPrice && discountPrice > 0">
                            <div class="text-h2 text-brand-warning">
                                {{ totalPrice - discountPrice | price }}
                            </div>
                            <b class="text-black">
                                Price:
                                <span class="text-line-through">{{ totalPrice | price }}</span>
                                <span class="c-discount-tag d-flex-inline text-top"
                                    v-if="product.quantityDiscount.type === 'percentPerItem'">
                                    <span class="c-discount-tag-corner"></span>
                                    <span class="c-discount-tag-label text-nowrap px-2 text-white bg-danger">
                                        {{ discount }}% off
                                    </span>
                                </span>
                                <span v-else>
                                    ({{ discountPrice | price }} off)
                                </span>
                            </b>
                        </template> -->
                        <span class="text-h2 text-brand-warning">
                            {{ totalPrice | price }}
                        </span>
                    </div>
                    <purchase-restriction-notification :product="product"></purchase-restriction-notification>
                    <select-product-variant v-if="variant && Object.keys(variant).length" :product="modifiedProduct"
                        :preview="preview" v-model="variant" :quantity="quantity" @change-quantity="quantity = $event">
                        <tr>
                            <td colspan="2" class="py-3">
                                <!-- Show signup button for public users -->
                                <template v-if="fakeUser">
                                    <button @click="$emit('auth')"
                                        class="btn btn-primary btn-block btn-lg"
                                        v-qa="'signup-btn'">
                                        Sign Up to Purchase
                                    </button>
                                </template>
                                <!-- Show cart buttons for authenticated users -->
                                <template v-else>
                                    <spinner-button @click="addToCart" :disabled="disabledBuyNow" v-qa="'addToCart-btn'"
                                        class="btn btn-warning btn-block btn-lg" :class="{ 'cursor-not-allowed': preview }"
                                        source="root" skip-saved>
                                        Add to Cart
                                    </spinner-button>
                                    <spinner-button @click="buyNow" :disabled="disabledBuyNow" v-qa="'addToCart-btn'"
                                        class="btn btn-info btn-block btn-lg" :class="{ 'cursor-not-allowed': preview }"
                                        source="root" eventName="goingToCart" skip-saved>
                                        Buy Now
                                    </spinner-button>
                                </template>
                            </td>
                        </tr>
                    </select-product-variant>



                    <div class="mb-3 text-wrap">
                        <span class="mr-1">
                            Vendor
                        </span>
                        <a :href="`/retailer/store/${product.vendor.slug}`" @click.stop.prevent="openVendorPage"
                            :class="{ 'cursor-not-allowed': preview }">
                            {{ product.vendor.storeName }}
                        </a>
                    </div>
                    <div class="d-flex align-items-center" v-if="!fakeUser">
                        <div>
                            <a class="d-flex cursor-pointer text-uppercase text-700"
                                :class="{ 'cursor-not-allowed': preview }" @click="openContactVendorModal">
                                <span class="mr-1">
                                    <img svg-inline class="icon" src="@images/inline-svg/icons/help.svg" alt="">
                                </span>
                                <span>
                                    Ask a Question
                                </span>
                            </a>
                        </div>
                    </div>

                </div>
            </div>
        </div>

        <div class="p-rel">
            <div class="c-tab-fade-left p-abs top-left bottom-left pl-3 z-index-3"></div>
            <div class="c-tab-fade-right p-abs top-right bottom-right pr-3 z-index-3"></div>
            <div class="c-tab-bottom-line p-abs bottom-left bottom-right"></div>
            <div style="overflow: auto;" class="p-rel">
                <div>
                    <ul class="nav nav-tabs align-items-stretch jus my-0 px-sm-3">
                        <li class="d-flex nav-item" :class="{ active: tab === 'overview' }">
                            <a href="" class="d-flex align-items-center justify-content-center text-nowrap nav-link"
                                style="padding-left: 8px; padding-right: 8px;" @click.prevent="tab = 'overview'">
                                <span>
                                    Overview
                                </span>
                            </a>
                        </li>
                        <li class="d-flex nav-item" :class="{ active: tab === 'description' }">
                            <a href="" class="d-flex align-items-center justify-content-center text-nowrap nav-link"
                                style="padding-left: 8px; padding-right: 8px;" @click.prevent="tab = 'description'">
                                <span>
                                    Description
                                </span>
                            </a>
                        </li>
                        <li class="d-flex nav-item" :class="{ active: tab === 'rating' }">
                            <a href="" class="d-flex align-items-center justify-content-center text-nowrap nav-link"
                                style="padding-left: 8px; padding-right: 8px;" @click.prevent="tab = 'rating'">
                                <span>
                                    Rating
                                </span>
                            </a>
                        </li>
                        <li class="d-flex nav-item" :class="{ active: tab === 'vendorRating' }">
                            <a href="" class="d-flex align-items-center justify-content-center text-nowrap nav-link"
                                style="padding-left: 8px; padding-right: 8px;" @click.prevent="tab = 'vendorRating'">
                                <span>
                                    Vendor Rating
                                </span>
                            </a>
                        </li>
                        <li class="d-flex nav-item" :class="{ active: tab === 'delivery' }">
                            <a href="" class="d-flex align-items-center justify-content-center text-nowrap nav-link"
                                style="padding-left: 8px; padding-right: 8px;" @click.prevent="tab = 'delivery'">
                                <span>
                                    Delivery
                                </span>
                            </a>
                        </li>
                        <li class="d-flex nav-item" :class="{ active: tab === 'quantityDiscount' }"
                            v-if="hasQuantityDiscountTab">
                            <a href="" class="d-flex align-items-center justify-content-center text-nowrap nav-link"
                                style="padding-left: 8px; padding-right: 8px;" @click.prevent="tab = 'quantityDiscount'">
                                <span>
                                    Wholesale Discount
                                </span>
                            </a>
                        </li>
                        <li class="d-flex nav-item pr-sm-3" :class="{ active: tab === 'quantityDiscount' }"
                            v-if="hasDocumentsTab">
                            <a href="" class="d-flex align-items-center justify-content-center text-nowrap nav-link"
                                style="padding-left: 8px; padding-right: 8px;" @click.prevent="tab = 'documents'">
                                <span>
                                    Documents
                                </span>
                            </a>
                        </li>
                        <li class="d-flex nav-item pr-3 hidden-md hidden-lg"></li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="tab-content">
            <div class="tab-pane active" v-if="tab === 'overview'">
                <overview-tab :product="product" :variant="variant" @switch-tab="tab = $event"
                    :preview="preview"></overview-tab>
            </div>
            <div class="tab-pane active" v-else-if="tab === 'description'">
                <description-tab :product="product" :variant="variant" :preview="preview"></description-tab>
            </div>
            <div class="tab-pane active" v-else-if="tab === 'rating'">
                <rating-tab :product="product" :preview="preview"></rating-tab>
            </div>
            <div class="tab-pane active" v-else-if="tab === 'vendorRating'">
                <vendor-rating-tab :product="product" :preview="preview"></vendor-rating-tab>
            </div>
            <div class="tab-pane active" v-else-if="tab === 'delivery'">
                <delivery-tab :product="product" :variant="variant" :preview="preview"></delivery-tab>
            </div>
            <div class="tab-pane active" v-else-if="tab === 'quantityDiscount'">
                <quantity-discount-tab :product="product" class="p-3 p-sm-6"></quantity-discount-tab>
            </div>
            <div class="tab-pane active" v-else-if="tab === 'documents'">
                <documents-tab :product="product" class="p-3 p-sm-6"></documents-tab>
            </div>
        </div>

    </div>
</template>

<script>
import Rating from '@/common/components/Rating';
import SpinnerButton from '@/common/components/SpinnerButton';
import ModalMixin from '@/common/plugins/modal/ModalMixin';
import DeliveryTab from '@/retailer/components/product/DeliveryTab';
import DescriptionTab from '@/retailer/components/product/DescriptionTab';
import DocumentsTab from '@/retailer/components/product/DocumentsTab';
import OverviewTab from '@/retailer/components/product/OverviewTab';
import QuantityDiscountTab from '@/retailer/components/product/QuantityDiscountTab';
import RatingTab from '@/retailer/components/product/RatingTab';
import SelectProductVariant from '@/retailer/components/product/SelectProductVariant';
import VendorRatingTab from '@/retailer/components/product/VendorRatingTab';
import PurchaseRestrictionNotification from './PurchaseRestrictionNotification';
// import { calcDiscount, findDiscount } from '@/retailer/utils';
import { mapActions, mapGetters } from 'vuex';
import ContactVendorModal from '../modals/ContactVendorModal';
import ImagesCarousel from './ImagesCarousel';

export default {
    name: 'RichProductCard',
    mixins: [ModalMixin],
    components: {
        DocumentsTab,
        SpinnerButton,
        SelectProductVariant,
        QuantityDiscountTab,
        DeliveryTab,
        VendorRatingTab,
        RatingTab,
        DescriptionTab,
        OverviewTab,
        ImagesCarousel,
        Rating,
        PurchaseRestrictionNotification
    },
    props: {
        product: {
            type: Object,
            require: true
        },
        preview: Boolean
    },
    data() {
        return {
            quantity: 0,
            variant: null,
            tab: 'overview'
        };
    },
    created() {
        // Reset state and select default variant
        this.resetState();
    },
    mounted() {
        this.modal.$once('close', () => this.$emit('close'));
    },
    beforeDestroy() {
        this.modal.$off('close');
    },
    computed: {
        ...mapGetters(['fakeUser']),
        filteredVariants() {
            // Remove COA requirement - all products are now visible regardless of COA status
            const filteredVariants = this.product.batches.flatMap(batch => {
                return batch.variants.filter(variant => {
                    const shouldShowWhenNoInventory = variant.showProductNoInventory || (!variant.showProductNoInventory && variant.quantity > 0);
                    return shouldShowWhenNoInventory;
                });
            });

            return filteredVariants;
        },
        modifiedProduct() {
            return {
                ...this.product,
                variants: this.filteredVariants
            };
        },
        hasQuantityDiscountTab() {
            return this.product.quantityDiscount.conditions && this.product.quantityDiscount.conditions.length;
        },
        hasDocumentsTab() {
            return Boolean((this.product.files || []).length);
        },
        disabledBuyNow() {
            const variantQuantity = this.variant ? this.variant.quantity : 0;
            return this.quantity > variantQuantity || this.errors.any();
        },
        totalPrice() {
            return this.variant && this.variant.price ? this.variant.price * this.quantity : 0;
        },
        // discount() {
        // TODO: Add discount price when coupon and product have one-to-one relationship
        // discountPrice() {
        //     return calcDiscount(this.product.quantityDiscount, this.totalPrice, this.quantity);
        // },
        //     const discount = findDiscount(this.product.quantityDiscount, this.quantity);
        //     return discount || 0;
        // }
    },
    methods: {
        ...mapActions(['addProductToCart']),
        validate() {
            return this.$validator.validateAll();
        },
        resetState() {
            // Reset all component state
            this.quantity = 0;
            this.variant = null;
            this.tab = 'overview';

            // Then select default variant
            this.selectDefaultVariantAndQuantity();
        },
        selectDefaultVariantAndQuantity() {
            if (this.filteredVariants && this.filteredVariants.length) {
                this.variant = this.filteredVariants.find((variant) => {
                    if (variant.quantity > 0) {
                        this.quantity = 1;
                        return true;
                    }
                    return false;
                });
            }
        },
        openVendorPage() {
            if (this.preview) {
                return;
            }
            this.modal.$off('close', this.back);
            this.hide();
            this.$router.push({ name: 'store', params: { storeNameSlug: this.product.vendor.slug } });
        },
        openContactVendorModal() {
            if (this.preview) {
                return;
            }
            this.$modal.open(ContactVendorModal, {
                size: 'modal-800',
                props: {
                    vendor: this.product.vendor,
                    product: this.product
                }
            });
        },
        addToCart() {
            if (this.preview) {
                return;
            }
            if (this.fakeUser) {
                return this.$emit('auth');
            }
            const promise = this.validate()
                .then(result => {
                    if (!result) {
                        return;
                    }
                    return this.addProductToCart({ product: this.product, quantity: this.quantity, variant: this.variant })
                        .then(() => {
                            this.modal.hide();
                        });
                });
            this.$root.$emit('loading', promise);
        },
        buyNow() {
            if (this.preview) {
                return;
            }
            if (this.fakeUser) {
                return this.$emit('auth');
            }
            const promise = this.validate()
                .then(result => {
                    if (!result) {
                        return;
                    }
                    return this.addProductToCart({ product: this.product, quantity: this.quantity, variant: this.variant })
                        .then(() => {
                            this.modal.$off('close');
                            this.hide();
                            this.$router.push('/cart');
                        });
                });

            this.$root.$emit('goingToCart', promise);
        }
    },
    watch: {
        filteredVariants(newVal) {
            this.$nextTick(() => {
                if (newVal && newVal.length) {
                    this.variant = newVal[0] || null;
                }
            });
        }
    }
};
</script>

<style scoped lang="scss">
@import "../../../common/stylesheets/variables";

.c-tab-bottom-line {
    border-bottom: 1px solid $nav-tabs-border-color;
}

.c-tab-fade-left {
    background: -moz-linear-gradient(left, rgba(255, 255, 255, 1) 0%, rgba(255, 255, 255, 0) 100%);
    background: -webkit-linear-gradient(left, rgba(255, 255, 255, 1) 0%, rgba(255, 255, 255, 0) 100%);
    background: linear-gradient(to right, rgba(255, 255, 255, 1) 0%, rgba(255, 255, 255, 0) 100%);
}

.c-tab-fade-right {
    background: -moz-linear-gradient(left, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 1) 100%);
    background: -webkit-linear-gradient(left, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 1) 100%);
    background: linear-gradient(to right, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 1) 100%);
}

.c-discount-tag-corner {
    line-height: #{$line-height-base}em;
    width: #{$line-height-base}em;
    position: relative;
    overflow: hidden;

    &:before {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        width: 100%;
        height: 100%;
        transform-origin: 0 0;
        transform: rotate(-45deg);
        background: $brand-danger;
    }
}

.c-discount-tag-label {
    line-height: #{$line-height-base}em;
    position: relative;

    &:before {
        content: '';
        position: absolute;
        left: -1px;
        top: 50%;
        width: 5px;
        height: 5px;
        border-radius: 5px;
        background: #fff;
        transform: translateY(-50%);
    }
}
</style>
