<template>
  <div v-if="showRestriction" class="purchase-restriction-alert">
    <div class="alert alert-warning" role="alert">
      <i class="fa fa-exclamation-triangle mr-2"></i>
      <strong>Purchase Restriction:</strong>
      {{ restrictionMessage }}
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex';

export default {
  name: 'PurchaseRestrictionNotification',
  props: {
    product: {
      type: Object,
      required: true
    }
  },
  computed: {
    ...mapGetters(['retailer', 'state']),
    showRestriction() {
      // Only show restriction for THC products when user can't purchase them
      if (!this.product.cannabisLicense) {
        return false;
      }
      
      // Don't show for public users (they see signup button instead)
      if (!this.retailer) {
        return false;
      }
      
      // Check if user is domestic and product is from different state
      if (this.state && this.state.isDomestic) {
        return !this.product._state || this.product._state !== this.retailer._state;
      }
      
      return false;
    },
    restrictionMessage() {
      if (this.state && this.state.isDomestic) {
        return 'THC products can only be purchased within your state. This product is not available for purchase in your location.';
      }
      return 'This product is not available for purchase in your location.';
    }
  }
};
</script>

<style scoped>
.purchase-restriction-alert {
  margin-bottom: 1rem;
}

.purchase-restriction-alert .alert {
  margin-bottom: 0;
  border-left: 4px solid #f0ad4e;
}

.purchase-restriction-alert .alert-warning {
  background-color: #fcf8e3;
  border-color: #f0ad4e;
  color: #8a6d3b;
}
</style>
