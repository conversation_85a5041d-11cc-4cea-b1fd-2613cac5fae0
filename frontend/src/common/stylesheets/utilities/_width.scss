@use "sass:math";

@for $i from 5 through 100 {
  @if $i % 5 == 0 {
    .w-#{$i} {
      width: percentage(math.div($i, 100));
    }
  }
}

.w-grid-condensed {
  width: math.div($grid-gutter-width, 2);
}

.w-grid {
  width: $grid-gutter-width;
}

// https://css-tricks.com/flexbox-truncated-text/
@for $i from 0 through 100 {
  @if $i % 5 == 0 {
    .min-w-#{$i} {
      min-width: percentage(math.div($i, 100));
    }
  }
}

@for $width from 1 through 15 {
  .w-em-#{$width} {
    width: #{$width}em;
  }

  .min-w-em-#{$width} {
    min-width: #{$width}em;
  }

  .max-w-em-#{$width} {
    max-width: #{$width}em;
  }
}

@each $breakpoint-abbr, $breakpoint in $screen-breakpoints-max {
  @media(min-width: $breakpoint + 1) {
    @for $width from 1 through 15 {
      .w-#{$breakpoint-abbr}-em-#{$width} {
        width: #{$width}em;
      }

      .min-w-#{$breakpoint-abbr}-em-#{$width} {
        min-width: #{$width}em;
      }
    }
  }
}

@each $breakpoint-abbr, $breakpoint in $grid-breakpoints {
  .max-w-#{$breakpoint-abbr} {
    max-width: $breakpoint;
  }
}

.max-w-iphone {
  max-width: 300px;
  margin-left: auto;
  margin-right: auto;
}
