@mixin bg($name, $color) {
  .bg-#{$name} {
    background: #{$color};
  }

  .cursor-pointer.bg-#{$name}:hover,
  a.bg-#{$name}:hover,
  a.bg-#{$name}:focus {
    background-color: darken($color, 3%);
  }

  .bg-hover-#{$name}:hover {
    background: #{$color};
  }
}

@each $color, $colors in $color-map {
  @each $label, $hex in $colors {
    // text colors
    @include text-emphasis-variant-custom('.text-#{inspect($color)}-#{$label}', $hex);

    // backgrounds
    @include bg((#{inspect($color)}-#{$label}), $hex);

    // borders
    .border-#{inspect($color)}-#{$label} {
      border: 1px solid #{$hex};
    }

    .hr-#{inspect($color)}-#{$label} {
      border-color: #{$hex};
    }

    .text-hover-#{inspect($color)}-#{$label}:hover {
      color: $hex;
    }
  }
}