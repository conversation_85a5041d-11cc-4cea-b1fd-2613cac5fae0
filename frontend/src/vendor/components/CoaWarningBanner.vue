<template>
  <div v-if="showWarning" class="coa-warning-banner">
    <div class="container-fluid">
      <div class="row">
        <div class="col-12">
          <div class="alert alert-warning alert-dismissible fade show" role="alert">
            <i class="fa fa-exclamation-triangle mr-2"></i>
            <strong>COA Required:</strong> 
            Products that don't have COA (Certificate of Analysis) will be removed. Add ASAP.
            <span v-if="productsCount > 0" class="ml-2">
              ({{ productsCount }} product{{ productsCount > 1 ? 's' : '' }} affected)
            </span>
            <button 
              type="button" 
              class="close" 
              @click="dismissWarning"
              aria-label="Close"
            >
              <span aria-hidden="true">&times;</span>
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import axios from 'axios';

export default {
  name: 'CoaWarningBanner',
  data() {
    return {
      showWarning: false,
      productsCount: 0,
      products: [],
      dismissed: false
    };
  },
  async mounted() {
    await this.checkCoaStatus();
  },
  methods: {
    async checkCoaStatus() {
      try {
        const response = await axios.get('/vendor/coa-warning');
        const { hasWarning, productsCount, products } = response.data;
        
        this.showWarning = hasWarning && !this.dismissed;
        this.productsCount = productsCount;
        this.products = products;
      } catch (error) {
        console.error('Error checking COA status:', error);
      }
    },
    dismissWarning() {
      this.dismissed = true;
      this.showWarning = false;
      // Store dismissal in session storage so it doesn't show again during this session
      sessionStorage.setItem('coaWarningDismissed', 'true');
    }
  },
  created() {
    // Check if warning was already dismissed in this session
    this.dismissed = sessionStorage.getItem('coaWarningDismissed') === 'true';
  }
};
</script>

<style scoped>
.coa-warning-banner {
  position: sticky;
  top: 0;
  z-index: 1000;
  background: #fff3cd;
  border-bottom: 1px solid #ffeaa7;
}

.coa-warning-banner .alert {
  margin-bottom: 0;
  border-radius: 0;
  border: none;
  background: #fff3cd;
  color: #856404;
}

.coa-warning-banner .alert-warning {
  border-color: #ffeaa7;
}

.coa-warning-banner .close {
  color: #856404;
  opacity: 0.8;
}

.coa-warning-banner .close:hover {
  opacity: 1;
}
</style>
