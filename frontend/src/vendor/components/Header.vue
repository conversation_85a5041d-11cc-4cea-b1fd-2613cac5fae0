<template>
    <div class="header-row">
        <div class="d-table-cell bg-white">
            <div class="header">
                <div class="container-fluid">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="d-flex align-items-center">

                                <!--menu icon (hamburger)-->
                                <div class="hidden-lg">
                                    <div class="d-flex align-items-center">
                                        <div class="text-gray-light cursor-pointer text-h3 mr-3"
                                            @click.prevent.stop="toggleSlideMenu()" v-qa="'hamburger-btn'">
                                            <svg class="icon d-block" viewBox="0 0 24 24">
                                                <path d="M3 18H21V16H3V18ZM3 13H21V11H3V13ZM3 6V8H21V6H3Z"
                                                    fill-rule="evenOdd"></path>
                                            </svg>
                                        </div>
                                    </div>
                                </div>

                                <!--Logo-->
                                <div>
                                    <div class="my-6">
                                        <router-link to="/home" class="text-white text-nodecor" v-qa="'logo'">
                                            <Logo />
                                        </router-link>
                                    </div>
                                </div>
                                <div class="mr-3"></div>

                                <!--Role switcher-->
                                <div class="dropdown mr-3 ml-minus-4">
                                    <div class="ml-4 text-vendor" v-qa="'currentRoleVendor'"><b>Vendor</b></div>
                                </div>

                                <!--Role switcher-->
                                <div class="dropdown mr-3 ml-minus-4" v-if="!this.state.isDomestic">
                                    <div class="ml-4 text-vendor" v-qa="'currentRoleVendor'"><b>International</b></div>
                                </div>

                                <!--state-->
                                <div class="hidden-sm hidden-xs mr-6">
                                    <div class="p-rel">
                                        <div class="d-flex align-items-center cursor-pointer"
                                            @click.prevent="openGeoFencedModal()" v-qa="'geoFenced-btn'">
                                            <div class="p-rel w-em-6">
                                                <div class="p-abs centered">
                                                    <img src="@images/retailer/image-geo-fenced.svg" class="img-block"
                                                        alt="">
                                                </div>
                                            </div>
                                            <div>
                                                <div class="small">
                                                    <div v-qa="'stateName'">{{ state.name }}</div>
                                                    <a class="cursor-pointer text-decor">Geo-fenced</a>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="ml-auto">
                            <div class="d-flex align-items-center">
                                <div>

                                    <div class="px-xs-3">
                                        <div class="d-flex align-items-center cursor-pointer text-brand-info border-radius-3"
                                            @click="openHelpModal" v-qa="'helpModal-btn'">
                                            <div class="text-size-27">
                                                <svg class="icon d-block mx-auto" viewBox="0 0 24 24">
                                                    <path
                                                        d="M11 18H13V16H11V18ZM12 2C6.48 2 2 6.48 2 12S6.48 22 12 22 22 17.52 22 12 17.52 2 12 2ZM12 20C7.59 20 4 16.41 4 12S7.59 4 12 4 20 7.59 20 12 16.41 20 12 20ZM12 6A4 4 0 0 0 8 10H10C10 8.9 10.9 8 12 8S14 8.9 14 10C14 12 11 11.75 11 15H13C13 12.75 16 12.5 16 10A4 4 0 0 0 12 6Z"
                                                        fill-rule="evenodd"></path>
                                                </svg>
                                            </div>
                                            <div class="text-center text-700 text-nowrap text-size-27 ml-1 hidden-xs">
                                                24/7 HELP
                                            </div>
                                        </div>
                                    </div>

                                </div>
                                <div>
                                    <div class="hidden-xs hidden-sm hidden-md">
                                        <div class="d-flex d-md-block align-items-center">
                                            <div class="dropdown">
                                                <div class="d-flex" :class="{ open: dropdowns['settings'] }">

                                                    <router-link to="/home" v-qa="'home-btn'"
                                                        class="d-flex align-items-center header-vendor-menu-link text-nodecor text-500">
                                                        <span class="d-flex align-items-center">
                                                            <span>
                                                                Home
                                                            </span>
                                                        </span>
                                                    </router-link>
                                                    <router-link to="/products" v-qa="'products-btn'"
                                                        class="d-flex align-items-center header-vendor-menu-link text-nodecor text-500">
                                                        <span class="d-flex align-items-center">
                                                            <span>
                                                                Products
                                                            </span>
                                                            <span class="ml-1" v-if="meta.products">
                                                                <span class="d-block header-vendor-menu-link-badge"
                                                                    v-qa="'productsCount'">{{ meta.products }}</span>
                                                            </span>
                                                        </span>
                                                    </router-link>
                                                    <router-link to="/orders" v-qa="'orders-btn'"
                                                        class="d-flex align-items-center header-vendor-menu-link text-nodecor text-500">
                                                        <span class="d-flex align-items-center">
                                                            <span>
                                                                Orders
                                                            </span>
                                                            <span class="ml-1" v-if="meta.orders">
                                                                <span class="d-block header-vendor-menu-link-badge"
                                                                    v-qa="'ordersCount'">{{ meta.orders }}</span>
                                                            </span>
                                                        </span>
                                                    </router-link>
                                                    <router-link to="/coupons"
                                                        class="d-flex align-items-center header-vendor-menu-link text-nodecor text-500">
                                                        <span class="d-flex align-items-center">
                                                            <span>
                                                                Coupons
                                                            </span>
                                                            <span class="ml-1" v-if="meta.coupons">
                                                                <span class="d-block header-vendor-menu-link-badge"
                                                                    v-qa="'couponsCount'">{{ meta.coupons }}</span>
                                                            </span>
                                                        </span>
                                                    </router-link>
                                                    <router-link to="/tickets" v-qa="'tickets-btn'"
                                                        class="d-flex align-items-center header-vendor-menu-link text-nodecor text-500">
                                                        <span class="d-flex align-items-center">
                                                            <span>
                                                                Tickets
                                                            </span>
                                                            <span class="ml-1" v-if="meta.tickets">
                                                                <span class="d-block header-vendor-menu-link-badge"
                                                                    v-qa="'ticketsCount'">{{ meta.tickets }}</span>
                                                            </span>
                                                        </span>
                                                    </router-link>

                                                    <ul class="dropdown-menu dropdown-menu-right" ref="dropdownMenu">

                                                        <router-link to="/settings" exact="" tag="li" active-class="active"
                                                            v-qa="'userSettings-btn'">
                                                            <a href>User Settings</a>
                                                        </router-link>
                                                        <router-link to="/settings/store" exact="" tag="li"
                                                            active-class="active" v-qa="'vendorSettings-btn'">
                                                            <a href>Vendor Settings</a>
                                                        </router-link>
                                                        <router-link to="/settings/team" exact="" tag="li"
                                                            active-class="active" v-qa="'teamSettings-btn'">
                                                            <a href>Team Settings</a>
                                                        </router-link>
                                                        <router-link to="/settings/payments" tag="li" active-class="active"
                                                            v-qa="'payoutSettings-btn'">
                                                            <a href>Payout Settings</a>
                                                        </router-link>
                                                        <router-link to="/settings/addresses" tag="li" active-class="active"
                                                            v-qa="'addresses-btn'">
                                                            <a href>Your Addresses</a>
                                                        </router-link>
                                                        <router-link to="/settings/return-policies" tag="li"
                                                            active-class="active" v-qa="'returnPolicies-btn'">
                                                            <a href>Return Policies</a>
                                                        </router-link>

                                                        <!--<router-link to="/settings/transactions" tag="li" active-class="active">-->
                                                        <!--<a href>Transactions</a>-->
                                                        <!--</router-link>-->
                                                        <li class="divider"></li>
                                                        <li>
                                                            <a type="button" class="cursor-pointer"
                                                                @click="logout('vendor')" v-qa="'logout-btn'">

                                                                <img svg-inline class="icon"
                                                                    src="@images/inline-svg/icons/exit.svg" alt="">
                                                                Logout

                                                            </a>
                                                        </li>
                                                    </ul>

                                                    <!--settings icon-->
                                                    <a ref="dropdownButton"
                                                        @click.stop.prevent="toggleDropdowns('settings')"
                                                        v-qa="'settings-btn'"
                                                        class="d-flex align-items-center header-vendor-menu-link text-nodecor text-500 text-brand-primar cursor-pointer text-noselect">
                                                        <span class="d-block p-rel">
                                                            &nbsp;
                                                            <span class="p-abs centered text-h4">
                                                                <img svg-inline class="icon"
                                                                    src="@images/inline-svg/icons/cog.svg" alt="">
                                                            </span>
                                                        </span>
                                                    </a>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="pl-3 min-w-0 hidden-xs hidden-sm hidden-md"
                                    v-if="availableRetailers.length || availableVendors.length">
                                    <div class="d-flex align-items-center dropdown"
                                        :class="{ open: dropdowns['accounts'] }">
                                        <a ref="dropdownButton" v-qa="'accounts-btn'"
                                            @click.stop.prevent="toggleDropdowns('accounts')"
                                            class="d-flex align-items-center text-nodecor text-500 cursor-pointer py-1 min-w-0">
                                            <div class="min-w-0 text-truncate">
                                                <div class="max-w-em-15 text-truncate">
                                                    <div
                                                        class="text-truncate text-right font-weight-700 font-size-16 line-height-19">
                                                        {{ vendor.storeName }}</div>
                                                    <div class="text-truncate text-right font-size-13 line-height-15">
                                                        {{ user.name }}</div>
                                                </div>
                                            </div>
                                            <div class="ml-1">
                                                <span class="caret"></span>
                                            </div>
                                        </a>

                                        <ul class="dropdown-menu dropdown-menu-right" ref="dropdownMenu">

                                            <template v-if="availableRetailers.length">
                                                <li class="dropdown-header">Retailers:</li>
                                                <li v-for="ret in availableRetailers" :key="ret.key">
                                                    <a class="cursor-pointer" @click="switchToAccountTo(ret)"
                                                        v-qa="'retailer-' + ret.key">
                                                        <span class="font-size-15">{{ ret.businessName }}</span>
                                                    </a>
                                                </li>
                                            </template>
                                            <li class="divider" v-if="availableRetailers.length && availableVendors.length">
                                            </li>
                                            <template>
                                                <li class="dropdown-header">Vendors:</li>
                                                <li class="active">
                                                    <a @click.prevent.stop v-qa="'vendor-' + vendor.key">
                                                        <span class="font-size-15">{{ vendor.storeName }}</span>
                                                    </a>
                                                </li>
                                                <li v-for="vend in availableVendors" :key="vend.key">
                                                    <a class="cursor-pointer" @click="switchToAccountTo(vend)"
                                                        v-qa="'vendor-' + vend.key">
                                                        <span class="font-size-15">{{ vend.storeName }}</span>
                                                    </a>
                                                </li>
                                            </template>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="header-vendor-menu-overlay" @click="isMenuOpen = false"></div>

                    </div>
                </div>
            </div>

            <div class="hidden-lg">
                <transition name="header-slide-menu">
                    <div class="header-slide-menu d-flex flex-column" v-show="isSlideMenuOpen" @click="closeSlideMenu()">
                        <div class="py-3">

                            <template v-if="availableRetailers.length || availableVendors.length">
                                <div class="px-4 text-truncate font-weight-700">{{ vendor.storeName }}</div>
                                <div class="px-4 text-truncate font-weight-400 font-size-14 line-height-16 mb-3">
                                    {{ user.name }}</div>

                                <div class="mt-3" v-if="availableRetailers.length">
                                    <div class="px-4 font-size-13 opacity-80 mb-1">Retailers:</div>
                                    <div v-for="ret in availableRetailers" :key="ret.key">
                                        <hr class="my-0 opacity-20">
                                        <a class="d-block text-primary px-4 py-3 text-nodecor cursor-pointer"
                                            @click="switchToAccountTo(ret)" v-qa="'retailer-' + ret.key">
                                            <span class="font-size-15">{{ ret.businessName }}</span>
                                        </a>
                                    </div>
                                </div>
                                <div class="mt-3">
                                    <div class="px-4 font-size-13 opacity-80 mb-1">Vendors:</div>
                                    <div>
                                        <hr class="my-0 opacity-20">
                                        <div class="px-4 py-3 bg-gray-3" v-qa="'vendor-' + vendor.key">
                                            <b class="font-size-15">{{ vendor.storeName }}</b>
                                        </div>
                                    </div>
                                    <div v-for="vend in availableVendors" :key="vend.key">
                                        <hr class="my-0 opacity-20">
                                        <a class="d-block text-primary px-4 py-3 text-nodecor cursor-pointer"
                                            @click="switchToAccountTo(vend)" v-qa="'vendor-' + vend.key">
                                            <span class="font-size-15">{{ vend.storeName }}</span>
                                        </a>
                                    </div>
                                </div>

                                <hr class="mt-3 mb-0">
                            </template>
                            <router-link :to="{ name: 'home' }" exact class="d-flex text-primary px-4 py-3 text-nodecor"
                                active-class="bg-gray-3" v-qa="'home-link'">
                                Home
                            </router-link>
                            <hr class="my-0 opacity-20">
                            <router-link to="/products"
                                class="d-flex text-primary px-4 py-3 text-nodecor justify-content-space-between"
                                active-class="bg-gray-3" v-qa="'products-link'">
                                <span class="min-w-0 text-truncate">
                                    Products
                                </span>
                                <span class="flex-shrink-0">
                                    <span class="d-block px-1 border-radius-20 bg-warning text-white text-700"
                                        v-if="meta.products">{{ meta.products }}</span>
                                </span>
                            </router-link>
                            <hr class="my-0 opacity-20">
                            <router-link to="/orders"
                                class="d-flex text-primary px-4 py-3 text-nodecor justify-content-space-between"
                                active-class="bg-gray-3" v-qa="'orders-link'">
                                <span class="min-w-0 text-truncate">
                                    Orders
                                </span>
                                <span class="flex-shrink-0">
                                    <span class="d-block px-1 border-radius-20 bg-warning text-white text-700"
                                        v-if="meta.orders">{{ meta.orders }}</span>
                                </span>
                            </router-link>
                            <hr class="my-0 opacity-20">
                            <router-link to="/tickets"
                                class="d-flex text-primary px-4 py-3 text-nodecor justify-content-space-between"
                                active-class="bg-gray-3" v-qa="'tickets-link'">
                                <span class="min-w-0 text-truncate">
                                    Tickets
                                </span>
                                <span class="flex-shrink-0">
                                    <span class="d-block px-1 border-radius-20 bg-warning text-white text-700"
                                        v-if="meta.tickets">{{ meta.tickets }}</span>
                                </span>
                            </router-link>

                            <hr class="my-0 opacity-20">

                            <router-link to="/settings" class="d-block text-primary px-4 py-3 text-nodecor"
                                active-class="bg-gray-3" v-qa="'userSettings-link'" exact>
                                Account Settings
                            </router-link>
                            <hr class="my-0 opacity-20">
                            <router-link to="/settings/store" class="d-block text-primary px-4 py-3 text-nodecor"
                                active-class="bg-gray-3" v-qa="'vendorSettings-link'" exact>
                                Vendor Settings
                            </router-link>
                            <hr class="my-0 opacity-20">
                            <router-link to="/settings/team" class="d-block text-primary px-4 py-3 text-nodecor"
                                active-class="bg-gray-3" v-qa="'teamSettings-link'" exact>
                                Team Settings
                            </router-link>
                            <hr class="my-0 opacity-20">
                            <router-link to="/settings/payments" class="d-block text-primary px-4 py-3 text-nodecor"
                                active-class="bg-gray-3" v-qa="'payoutSettings-link'" exact>
                                Payout Settings
                            </router-link>
                            <hr class="my-0 opacity-20">
                            <router-link to="/settings/addresses" class="d-block text-primary px-4 py-3 text-nodecor"
                                active-class="bg-gray-3" v-qa="'addresses-link'" exact>
                                Your Addresses
                            </router-link>
                            <hr class="my-0 opacity-20">
                            <router-link to="/settings/return-policies" class="d-block text-primary px-4 py-3 text-nodecor"
                                active-class="bg-gray-3" v-qa="'returnPolicies-link'" exact>
                                Return Policies
                            </router-link>

                        </div>

                        <div class="mt-auto">
                            <div class="pb-3">

                                <hr class="my-0" />

                                <a class="d-block text-primary px-4 py-3 text-nodecor cursor-pointer"
                                    active-class="bg-gray-3" v-qa="'logout-link'" @click="logout('vendor')">
                                    <img svg-inline class="icon" src="@images/inline-svg/icons/exit.svg" alt="">
                                    Logout
                                </a>
                            </div>
                        </div>
                    </div>
                </transition>
                <transition name="header-slide-menu-overlay">
                    <div class="header-slide-menu-overlay" v-show="isSlideMenuOpen" @click="closeSlideMenu()"></div>
                </transition>
            </div>

        </div>
    </div>
</template>

<script>
import GeoFencedModal from '@/common/components/GeoFencedModal';
import HelpModal from '@/common/components/HelpModal.vue';
import Logo from '@/common/components/Logo';
import { mapActions, mapGetters, mapState } from 'vuex';

export default {
    name: 'Header',
    inject: ['$vendorService'],
    components: {
        Logo
    },
    data() {
        return {
            dropdowns: {},
            isSlideMenuOpen: false
        };
    },
    watch: {
        dropdowns() {
            if (Object.keys(this.dropdowns).length) {
                document.addEventListener('click', this.closeDropdowns);
            }
        }
    },
    computed: {
        ...mapState(['meta', 'vendor']),
        ...mapGetters(['user', 'state', 'availableRetailers', 'availableVendors'])
    },
    methods: {
        ...mapActions(['logout']),
        switchToAccountTo(data) {
            this.$vendorService.switchAccount(data)
                .then(url => {
                    window.location.href = url;
                })
                .catch(err => {
                    this.$flashError(err);
                });
        },
        openGeoFencedModal() {
            this.$modal.open(GeoFencedModal, { size: 'modal-800' });
        },
        openHelpModal() {
            this.$modal.open(HelpModal, {
                size: 'modal-800',
                props: {
                    role: 'vendor'
                }
            });
        },
        toggleSlideMenu() {
            this.isSlideMenuOpen = !this.isSlideMenuOpen;
        },
        closeSlideMenu() {
            this.isSlideMenuOpen = false;
        },
        closeDropdowns() {
            this.dropdowns = {};
        },
        toggleDropdowns(name) {
            const state = this.dropdowns[name];

            this.dropdowns = {
                [name]: !state
            };
        }
    }
};
</script>
