<template>
    <div>
        <div class="panel panel-default mb-0">
            <div class="panel-heading">
                <h4 class="panel-title text-700">
                    <router-link to="/vendors" class="text-link"><span class="text-link">Vendors</span></router-link>
                    <svg class="icon text-muted" viewBox="0 0 24 24">
                        <path d="M8.59 16.34L13.17 11.75 8.59 7.16 10 5.75 16 11.75 10 17.75Z" fill-rule="evenOdd"></path>
                    </svg>
                    {{ $route.params.key }}
                </h4>
            </div>
            <div class="panel-body" v-if="!showNotFound && !loading">

                <div class="row-condensed">
                    <div class="col-sm-8">
                        <div class="table-responsive">
                            <table class="table table-bordered mb-0">
                                <tbody>
                                    <tr>
                                        <td class="w-30">
                                            ID
                                        </td>
                                        <td v-qa="vendor.key">
                                            {{ vendor.key }}
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>
                                            State
                                        </td>
                                        <td>
                                            <state :value="vendor.state" />
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>
                                            Retailer Name
                                        </td>
                                        <td>
                                            <router-link :to="`/retailers/${vendor.retailer.key}`"
                                                v-qa="'retailer-link'">{{ vendor.retailer.businessName }}</router-link>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>
                                            <div class="form-control-static">
                                                Broker Account
                                            </div>
                                        </td>
                                        <td>
                                            <div class="form-inline text-middle">
                                                <div class="form-control-select">
                                                    <select class="form-control" v-model="vendor.isBroker">
                                                        <option :value="true">Yes</option>
                                                        <option :value="false">No</option>
                                                    </select>
                                                    <span class="form-control-select-arrow">
                                                        <span class="caret"></span>
                                                    </span>
                                                </div>
                                            </div>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>
                                            Vendor Name
                                        </td>
                                        <td>
                                            <a :href="storeUrl" target="_blank"
                                                class="text-decor text-pre-wrap-break-word">{{ vendor.storeName }}<img
                                                    svg-inline class="icon ml-1"
                                                    src="@images/inline-svg/icons/open-in-new.svg" alt=""></a>
                                        </td>
                                    </tr>

                                    <!--start cvv added-->
                                    <tr v-if="isDomestic">
                                        <td>
                                            Metrc Integration
                                        </td>
                                        <td>
                                            <span style="color:green;font-weight:bold;">CONNECTED</span>
                                        </td>
                                    </tr>
                                    <!--stop cvv added-->

                                    <tr>
                                        <td>
                                            # Active Products
                                        </td>
                                        <td>
                                            {{ vendor.activeProductsCount }}
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>
                                            # Inactive Product
                                        </td>
                                        <td>
                                            {{ vendor.inactiveProductsCount }}
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>
                                            # Product Images
                                        </td>
                                        <td>
                                            {{ vendor.productImagesCount }}
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>
                                            # of Products without Images
                                        </td>
                                        <td>
                                            {{ vendor.productWithoutImagesCount }}
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>
                                            % of Products without Images
                                        </td>
                                        <td>
                                            {{ vendor.productWithoutImagesPercent }}%
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>
                                            Logo
                                        </td>
                                        <td>
                                            <image-preview v-if="vendor.logo.uuid" :image="vendor.logo" />
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>
                                            Description
                                        </td>
                                        <td>
                                            {{ vendor.description }}
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>
                                            <div class="form-control-static">
                                                Enable THC Product Sales
                                                <small class="text-muted d-block">
                                                    Controls whether vendor can sell THC products.
                                                    THC products are visible to all users but purchase restrictions apply based on state.
                                                    Vendors should have valid cannabis license to sell THC products.
                                                </small>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="form-inline text-middle">
                                                <div class="form-control-select">
                                                    <select class="form-control" v-model="vendor.showThc">
                                                        <option :value="true">Yes</option>
                                                        <option :value="false">No</option>
                                                    </select>
                                                    <span class="form-control-select-arrow">
                                                        <span class="caret"></span>
                                                    </span>
                                                </div>
                                            </div>
                                        </td>
                                    </tr>


                                    <tr>
                                        <td>
                                            Application Status
                                        </td>
                                        <td>
                                            <template v-if="vendor.application">{{ vendor.application.status }}</template>
                                            <template v-else>Not Submitted</template>
                                        </td>
                                    </tr>
                                    <tr v-if="vendor.application">
                                        <td>
                                            Application ID
                                        </td>
                                        <td>
                                            <router-link :to="`/applications/${vendor.application.key}`"
                                                v-qa="'application-link'">
                                                {{ vendor.application.key }}
                                            </router-link>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>
                                            Created At
                                        </td>
                                        <td>
                                            <div class="text-nowrap">{{ vendor.createdAt | date }}</div>
                                            <div class="text-size-13 opacity-60">
                                                {{ vendor.createdAt | time }}
                                            </div>
                                        </td>
                                    </tr>

                                    <!--start cvv added-->
                                    <tr>
                                        <td>
                                            Compliance
                                        </td>
                                        <td v-if="isDomestic">
                                            <span style="color:green;font-weight:bold;font-size:18pt;">VALID</span>
                                            <div>California License</div>
                                            <div>239483249827349</div>
                                            <div class="text-size-13 opacity-60">
                                                The Bureau of Cannabis Control
                                            </div>
                                            <div class="text-size-13 opacity-60">
                                                Expires Apr 26, 2022
                                            </div>
                                        </td>
                                        <td v-if="!isDomestic">
                                            <span style="font-weight:bold;font-size:18pt;">Pending</span>
                                            <!-- <div>California License</div>
                                            <div>239483249827349</div>
                                            <div class="text-size-13 opacity-60">
                                                The Bureau of Cannabis Control
                                            </div>
                                            <div class="text-size-13 opacity-60">
                                                Expires Apr 26, 2022
                                            </div> -->
                                        </td>
                                    </tr>
                                    <!--stop cvv added-->

                                    <tr>
                                        <td>
                                            <div class="form-control-static">
                                                Return Probability
                                            </div>
                                        </td>
                                        <td>
                                            <div class="form-inline text-middle">
                                                <div class="form-control-select">
                                                    <select class="form-control" v-model="vendor.returnProbability">
                                                        <option value="low">Low</option>
                                                        <option value="medium">Medium</option>
                                                        <option value="high">High</option>
                                                        <option value="veryHigh">Very High</option>
                                                    </select>
                                                    <span class="form-control-select-arrow">
                                                        <span class="caret"></span>
                                                    </span>
                                                </div>
                                            </div>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>
                                            <div class="form-control-static">
                                                Vendor Account
                                            </div>
                                        </td>
                                        <td>
                                            <div class="form-inline text-middle">
                                                <div class="form-control-select">
                                                    <select class="form-control" v-model="vendor.enabled"
                                                        v-qa="'vendorRole'">
                                                        <option :value="true">Yes</option>
                                                        <option :value="false">No</option>
                                                    </select>
                                                    <span class="form-control-select-arrow">
                                                        <span class="caret"></span>
                                                    </span>
                                                </div>
                                            </div>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>
                                            <div class="mt-1">
                                                Revenue Share
                                            </div>
                                        </td>
                                        <td>
                                            <div class="form-inline w-em-7">
                                                <div class="input-group">
                                                    <input-error type="number" class="form-control" v-model="revenueShare"
                                                        name="revenueShare"
                                                        v-validate="{ required: true, max_value: 100, min_value: 0 }" />
                                                    <span class="input-group-addon">%</span>
                                                </div>
                                            </div>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>
                                            How many products do you expect to upload?
                                        </td>
                                        <td>
                                            {{ vendor.vendorProductCount || '–' }}
                                        </td>
                                    </tr>

                                    <template v-if="vendor.payment.method === 'check'">
                                        <tr>
                                            <td>
                                                Payout Method
                                            </td>
                                            <td>
                                                Mail a Check
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>
                                                Payable To
                                            </td>
                                            <td>
                                                {{ vendor.payment.check.payableTo || '–' }}
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>
                                                Mailing Address
                                            </td>
                                            <td>
                                                {{ vendor.payment.check.mailingAddress || '–' }}
                                            </td>
                                        </tr>
                                    </template>
                                    <template v-if="vendor.payment.method === 'ach'">
                                        <tr>
                                            <td>
                                                Payout Method
                                            </td>
                                            <td>
                                                ACH
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>
                                                Bank Name
                                            </td>
                                            <td>
                                                {{ vendor.payment.ach.name || '–' }}
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>
                                                Routing Number
                                            </td>
                                            <td>
                                                {{ vendor.payment.ach.routing || '–' }}
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>
                                                Account Number
                                            </td>
                                            <td>
                                                {{ vendor.payment.ach.accountNumber || '–' }}
                                            </td>
                                        </tr>
                                    </template>
                                    <template v-if="vendor.payment.method === 'wire_transfer'">
                                        <tr>
                                            <td>
                                                Payout Method
                                            </td>
                                            <td>
                                                Wire Transfer
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>
                                                Bank Name
                                            </td>
                                            <td>
                                                {{ vendor.payment.wire_transfer.name || '–' }}
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>
                                                Routing Number
                                            </td>
                                            <td>
                                                {{ vendor.payment.wire_transfer.routing || '–' }}
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>
                                                Account Number
                                            </td>
                                            <td>
                                                {{ vendor.payment.wire_transfer.accountNumber || '–' }}
                                            </td>
                                        </tr>
                                    </template>
                                    <tr>
                                        <td>Last Accepted Terms</td>
                                        <td v-if="vendor.lastAgreeTermsAt" v-qa="'lastAgreeTermsAt'">
                                            <div class="text-nowrap">{{ vendor.lastAgreeTermsAt | date }}</div>
                                            <div class="text-size-13 opacity-60">
                                                {{ vendor.lastAgreeTermsAt | time }}
                                            </div>
                                        </td>
                                        <td v-else>
                                            –
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>
                                            Team
                                        </td>
                                        <td>
                                            <div v-for="member in vendor.team"
                                                :key="member.user && member.user.email || member.email">
                                                <router-link v-if="member.user && member.user.key"
                                                    :to="{ path: `/users/${member.user.key}` }" class="text-wrap"
                                                    v-qa="'user-link'">
                                                    {{ member.user.name }} {{ member.status }}
                                                </router-link>
                                                <span v-else>
                                                    {{ member.name }} - {{ member.email }} invited
                                                </span>
                                            </div>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>
                                            <div class="mt-1">
                                                Admin Notes
                                            </div>
                                        </td>
                                        <td>
                                            <textarea class="form-control" id="" cols="30" rows="5"
                                                v-model="vendor.adminNotes">Call to verify</textarea>
                                        </td>
                                    </tr>                                    
                                </tbody>
                            </table>
                        </div>
                    </div>
                    <div class="mt-3 visible-xs-block"></div>
                    <div class="col-sm-4">
                        <a :href="`/admin/users/${vendor.key}/login`" class="btn btn-default btn-block text-wrap "
                            target="_blank">
                            <span class="d-flex-inline text-middle">
                                <span class="mr-1">
                                    <img svg-inline class="icon mr-1" src="@images/inline-svg/icons/lock.svg" alt="">
                                </span>
                                <span>
                                    Login as {{ vendor.storeName }}
                                </span>
                            </span>
                        </a>
                        <router-link :to="{ path: '/applications', query: { 'q-vendor': vendor.key } }"
                            class="btn btn-default btn-block text-wrap ">
                            <span class="d-flex-inline text-middle">
                                <span class="mr-1">
                                    <img svg-inline class="icon mr-1" src="@images/inline-svg/icons/profile.svg" alt="">
                                </span>
                                <span>
                                    See Applications
                                </span>
                            </span>
                        </router-link>
                        <router-link :to="{ path: '/products', query: { 'q-vendor': vendor.key } }"
                            class="btn btn-default btn-block text-wrap ">
                            <span class="d-flex-inline text-middle">
                                <span class="mr-1">
                                    <img svg-inline class="icon mr-1" src="@images/inline-svg/icons/products.svg" alt="">
                                </span>
                                <span>
                                    See Products
                                </span>
                            </span>
                        </router-link>
                        <router-link :to="{ path: '/vendor-orders', query: { 'q-vendor': vendor.key } }"
                            class="btn btn-default btn-block text-wrap ">
                            <span class="d-flex-inline text-middle">
                                <span class="mr-1">
                                    <img svg-inline class="icon mr-1" src="@images/inline-svg/icons/cart-full.svg" alt="">
                                </span>
                                <span>
                                    See Vendor Orders
                                </span>
                            </span>
                        </router-link>
                        <router-link :to="{ path: '/tickets', query: { 'q-vendor': vendor.key } }"
                            class="btn btn-default btn-block text-wrap ">
                            <span class="d-flex-inline text-middle">
                                <span class="mr-1">
                                    <img svg-inline class="icon" src="@images/inline-svg/icons/mail-empty.svg" alt="">
                                </span>
                                <span>
                                    See Tickets
                                </span>
                            </span>
                        </router-link>
                        <router-link :to="{ path: '/reviews', query: { 'q-vendor': vendor.key } }"
                            class="btn btn-default btn-block text-wrap ">
                            <span class="d-flex-inline text-middle">
                                <span class="mr-1">

                                </span>
                                <span>
                                    See Reviews
                                </span>
                            </span>
                        </router-link>
                    </div>
                </div>

                <div class="row-condensed">
                    <div class="col-sm-8">
                        <div class="mt-3">
                            <button type="button" class="btn btn-primary btn-width-xs btn-xs-block" @click="save()"
                                v-qa="'save-btn'">
                                Save
                            </button>
                        </div>
                    </div>
                </div>


            </div>
            <div v-else-if="!loading">
                <h2>Vendor Not Found</h2>
            </div>
        </div>
    </div>
</template>

<script>
// import Multiselect from 'vue-multiselect';

export default {
    name: 'VendorPage',
    inject: ['$vendorService', '$documentsService'],
    // components: { Multiselect },
    data() {
        return {
            loading: true,
            showNotFound: false,
            vendor: {},
            isDomestic: true,
            requiredDocuments: [],
            documentTypes: []
        };
    },
    created() {
        this.$vendorService.getVendorById(this.$route.params.key)
            .then(({ vendor }) => {
                this.vendor = { ...vendor };
                this.isDomestic = this.$store.state.states.find(_state => _state.id === vendor.state).isDomestic;
                this.loading = false;
            })
            .catch(() => {
                this.showNotFound = true;
            });
    },
    computed: {
        revenueShare: {
            get() {
                return this.vendor.revenueShare / 100;
            },
            set(value) {
                this.vendor.revenueShare = Math.round(value * 100);
            }
        },
        storeUrl() {
            return `${window.location.origin}/admin/store/${this.vendor.slug}`;
        }
    },
    methods: {
        save() {
            this.$vendorService.saveVendor(this.vendor.key, {
                isBroker: this.vendor.isBroker,
                returnProbability: this.vendor.returnProbability,
                revenueShare: this.vendor.revenueShare,
                enabled: this.vendor.enabled,
                showThc: this.vendor.showThc,
                adminNotes: this.vendor.adminNotes,
            })
                .then(vendor => {
                    this.vendor = vendor;
                    this.$flashSuccess('Changes has been saved successfully.');
                })
                .catch(err => {
                    this.$flashError(err);
                });
        }
    }
};
</script>
