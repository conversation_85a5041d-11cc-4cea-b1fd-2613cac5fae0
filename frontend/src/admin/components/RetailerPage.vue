<template>
    <div>
        <div class="panel panel-default mb-0">
            <div class="panel-heading">
                <h4 class="panel-title text-700">
                    <router-link to="/retailers" class="text-link"><span class="text-link">Retailers</span>
                    </router-link>
                    <svg class="icon text-muted" viewBox="0 0 24 24">
                        <path d="M8.59 16.34L13.17 11.75 8.59 7.16 10 5.75 16 11.75 10 17.75Z" fill-rule="evenOdd"></path>
                    </svg>
                    {{ $route.params.key }}
                </h4>
            </div>
            <div class="panel-body" v-if="!showNotFound && !loading">

                <div class="row-condensed">
                    <div class="col-sm-8">
                        <div class="table-responsive">
                            <table class="table table-bordered mb-0">
                                <tbody>
                                    <tr>
                                        <td class="w-30">
                                            ID
                                        </td>
                                        <td v-qa="retailer.key">
                                            {{ retailer.key }}
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="w-30">
                                            Retailer Name
                                        </td>
                                        <td>
                                            {{ retailer.businessName }}
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>
                                            State
                                        </td>
                                        <td>
                                            <state :value="retailer.state" />
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>
                                            Vendor Name
                                        </td>
                                        <td>
                                            <router-link :to="`/vendors/${retailer.vendor.key}`" v-qa="'vendor-link'">{{
                                                retailer.vendor.storeName }}</router-link>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>
                                            Application Status
                                        </td>
                                        <td>
                                            <template v-if="retailer.application">{{ retailer.application.status
                                            }}</template>
                                            <template v-else>Not Submitted</template>
                                        </td>
                                    </tr>
                                    <tr v-if="retailer.application">
                                        <td>
                                            Application ID
                                        </td>
                                        <td>
                                            <router-link :to="`/applications/${retailer.application.key}`"
                                                v-qa="'application-link'">
                                                {{ retailer.application.key }}
                                            </router-link>
                                        </td>
                                    </tr>

                                    <tr>
                                        <td>
                                            Created At
                                        </td>
                                        <td>
                                            <div class="text-nowrap">{{ retailer.createdAt | date }}</div>
                                            <div class="text-size-13 opacity-60">
                                                {{ retailer.createdAt | time }}
                                            </div>
                                        </td>
                                    </tr>

                                    <!--start cvv added-->
                                    <tr>
                                        <td>
                                            Compliance
                                        </td>
                                        <td v-if="isDomestic">
                                            <span style="color:green;font-weight:bold;font-size:18pt;">VALID</span>
                                            <div>California License</div>
                                            <div>239483249827349</div>
                                            <div class="text-size-13 opacity-60">
                                                The Bureau of Cannabis Control
                                            </div>
                                            <div class="text-size-13 opacity-60">
                                                Expires Apr 26, 2022
                                            </div>
                                        </td>
                                        <td v-if="!isDomestic">
                                            <span style="font-weight:bold;font-size:18pt;">Pending</span>
                                            <!-- <div>California License</div>
                                            <div>239483249827349</div>
                                            <div class="text-size-13 opacity-60">
                                                The Bureau of Cannabis Control
                                            </div>
                                            <div class="text-size-13 opacity-60">
                                                Expires Apr 26, 2022
                                            </div> -->
                                        </td>
                                    </tr>
                                    <!--stop cvv added-->

                                    <tr>
                                        <td>
                                            <div class="form-control-static">
                                                Enable THC Product Purchasing
                                                <small class="text-muted d-block">
                                                    Controls whether retailer can purchase THC products.
                                                    Domestic retailers can only purchase from their state.
                                                    All users can view THC products regardless of this setting.
                                                </small>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="form-inline text-middle">
                                                <div class="form-control-select">
                                                    <select class="form-control" v-model="retailer.showThc">
                                                        <option :value="true">Yes</option>
                                                        <option :value="false">No</option>
                                                    </select>
                                                    <span class="form-control-select-arrow">
                                                        <span class="caret"></span>
                                                    </span>
                                                </div>
                                            </div>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>
                                            <div class="form-control-static">
                                                What is your expected monthly budget?
                                            </div>
                                        </td>
                                        <td>
                                            <div class="form-inline text-middle">
                                                <div class="form-control-select">
                                                    {{ retailer.retailerBudget || '–' }}
                                                </div>
                                            </div>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>
                                            <div class="form-control-static">
                                                Retailer Account
                                            </div>
                                        </td>
                                        <td>
                                            <div class="form-inline text-middle">
                                                <div class="form-control-select">
                                                    <select class="form-control" v-model="retailer.enabled"
                                                        v-qa="'retailerRole'">
                                                        <option :value="true">Yes</option>
                                                        <option :value="false">No</option>
                                                    </select>
                                                    <span class="form-control-select-arrow">
                                                        <span class="caret"></span>
                                                    </span>
                                                </div>
                                            </div>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>Last Accepted Terms</td>
                                        <td v-if="retailer.lastAgreeTermsAt" v-qa="'lastAgreeTermsAt'">
                                            <div class="text-nowrap">{{ retailer.lastAgreeTermsAt | date }}</div>
                                            <div class="text-size-13 opacity-60">
                                                {{ retailer.lastAgreeTermsAt | time }}
                                            </div>
                                        </td>
                                        <td v-else>
                                            –
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>
                                            Team
                                        </td>
                                        <td>
                                            <div v-for="member in retailer.team"
                                                :key="member.user && member.user.email || member.email">
                                                <router-link v-if="member.user && member.user.key"
                                                    :to="{ path: `/users/${member.user.key}` }" class="text-wrap"
                                                    v-qa="'user-link'">
                                                    {{ member.user.name }} {{ member.status }}
                                                </router-link>
                                                <span v-else>
                                                    {{ member.name }} - {{ member.email }} invited
                                                </span>
                                            </div>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>
                                            <div class="mt-1">
                                                Admin Notes
                                            </div>
                                        </td>
                                        <td>
                                            <textarea class="form-control" id="" cols="30" rows="5"
                                                v-model="retailer.adminNotes">Call to verify</textarea>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                    <div class="mt-3 visible-xs-block"></div>
                    <div class="col-sm-4">
                        <a :href="`/admin/users/${retailer.key}/login`" class="btn btn-default btn-block text-wrap "
                            target="_blank">
                            <span class="d-flex-inline text-middle">
                                <span class="mr-1">
                                    <img svg-inline class="icon mr-1" src="@images/inline-svg/icons/lock.svg" alt="">
                                </span>
                                <span>
                                    Login as {{ retailer.businessName }}
                                </span>
                            </span>
                        </a>
                        <router-link :to="{ path: '/applications', query: { 'q-retailer': retailer.key } }"
                            class="btn btn-default btn-block text-wrap ">
                            <span class="d-flex-inline text-middle">
                                <span class="mr-1">
                                    <img svg-inline class="icon mr-1" src="@images/inline-svg/icons/profile.svg" alt="">
                                </span>
                                <span>
                                    See Applications
                                </span>
                            </span>
                        </router-link>
                        <router-link :to="{ path: '/retailer-orders', query: { 'q-retailer': retailer.key } }"
                            class="btn btn-default btn-block text-wrap ">
                            <span class="d-flex-inline text-middle">
                                <span class="mr-1">
                                    <img svg-inline class="icon mr-1" src="@images/inline-svg/icons/cart-empty.svg" alt="">
                                </span>
                                <span>
                                    See Retailer Orders
                                </span>
                            </span>
                        </router-link>
                        <router-link :to="{ path: '/tickets', query: { 'q-retailer': retailer.key } }"
                            class="btn btn-default btn-block text-wrap ">
                            <span class="d-flex-inline text-middle">
                                <span class="mr-1">
                                    <img svg-inline class="icon mr-1" src="@images/inline-svg/icons/mail-empty.svg" alt="">
                                </span>
                                <span>
                                    See Tickets
                                </span>
                            </span>
                        </router-link>
                        <router-link :to="{ path: '/reviews', query: { 'q-retailer': retailer.key } }"
                            class="btn btn-default btn-block text-wrap ">
                            <span class="d-flex-inline text-middle">
                                <span class="mr-1">
                                    <img svg-inline class="icon mr-1" src="@images/inline-svg/icons/review.svg" alt="">
                                </span>
                                <span>
                                    See Reviews
                                </span>
                            </span>
                        </router-link>
                    </div>
                </div>

                <div class="row-condensed">
                    <div class="col-sm-8">
                        <div class="mt-3">
                            <button type="button" class="btn btn-primary btn-width-xs btn-xs-block" @click="save()"
                                v-qa="'save-btn'">
                                Save
                            </button>
                        </div>
                    </div>
                </div>


            </div>
            <div v-else-if="!loading">
                <h2>Retailer Not Found</h2>
            </div>
        </div>
    </div>
</template>

<script>


export default {
    name: 'RetailerPage',
    inject: ['$retailerService', '$documentsService'],
    data() {
        return {
            loading: true,
            showNotFound: false,
            retailer: {},
            isDomestic: true,
            documentTypes: []
        };
    },
    created() {
        this.$retailerService.getRetailerById(this.$route.params.key)
            .then(({ retailer }) => {
                this.retailer = { ...retailer };
                this.loading = false;
                this.isDomestic = this.$store.state.states.find(_state => _state.id === retailer.state).isDomestic;
                // this.requiredDocuments = retailer.requiredDocuments;
            })
            .catch(() => {
                this.showNotFound = true;
            });
    },
    methods: {
        save() {
            this.$retailerService.saveRetailer(this.retailer.key, {
                showThc: this.retailer.showThc,
                enabled: this.retailer.enabled,
                adminNotes: this.retailer.adminNotes,
            })
                .then(retailer => {
                    this.retailer = retailer;
                    this.$flashSuccess('Changes has been saved successfully.');
                })
                .catch(err => {
                    this.$flashError(err);
                });
        }
    }
};
</script>
