#!/usr/bin/env node
'use strict';

/**
 * Simple test script to verify THC filtering functionality
 * This tests the backend API endpoints directly without browser automation
 */

const axios = require('axios');
const mongoose = require('mongoose');
const config = require('./backend/src/services/config');

// Test configuration
const BASE_URL = config.app.url || 'http://localhost:3000';
const TEST_RESULTS = {
    passed: 0,
    failed: 0,
    tests: []
};

function logTest(name, passed, message = '') {
    TEST_RESULTS.tests.push({ name, passed, message });
    if (passed) {
        TEST_RESULTS.passed++;
        console.log(`✅ ${name}`);
    } else {
        TEST_RESULTS.failed++;
        console.log(`❌ ${name}: ${message}`);
    }
}

async function testPublicStoreAccess() {
    console.log('\n🧪 Testing Public Store Access...');
    
    try {
        // Test accessing a public store without authentication
        const response = await axios.get(`${BASE_URL}/retailer/store/test-store`, {
            validateStatus: () => true // Don't throw on 4xx/5xx
        });
        
        if (response.status === 200 || response.status === 404) {
            logTest('Public store access endpoint responds', true);
        } else {
            logTest('Public store access endpoint responds', false, `Status: ${response.status}`);
        }
    } catch (error) {
        logTest('Public store access endpoint responds', false, error.message);
    }
}

async function testCoaWarningEndpoint() {
    console.log('\n🧪 Testing COA Warning Endpoint...');
    
    try {
        // Test COA warning endpoint (requires authentication, so expect 401/403)
        const response = await axios.get(`${BASE_URL}/vendor/coa-warning`, {
            validateStatus: () => true
        });
        
        if (response.status === 401 || response.status === 403) {
            logTest('COA warning endpoint exists and requires auth', true);
        } else if (response.status === 200) {
            logTest('COA warning endpoint exists and accessible', true);
        } else {
            logTest('COA warning endpoint exists', false, `Status: ${response.status}`);
        }
    } catch (error) {
        logTest('COA warning endpoint exists', false, error.message);
    }
}

async function testProductFiltering() {
    console.log('\n🧪 Testing Product Filtering...');
    
    try {
        // Test product search endpoint
        const response = await axios.get(`${BASE_URL}/retailer/products`, {
            validateStatus: () => true
        });
        
        if (response.status === 200 || response.status === 401) {
            logTest('Product filtering endpoint responds', true);
        } else {
            logTest('Product filtering endpoint responds', false, `Status: ${response.status}`);
        }
    } catch (error) {
        logTest('Product filtering endpoint responds', false, error.message);
    }
}

async function testCartValidation() {
    console.log('\n🧪 Testing Cart Validation...');
    
    try {
        // Test cart restrictions endpoint
        const response = await axios.get(`${BASE_URL}/retailer/cart/restrictions`, {
            validateStatus: () => true
        });
        
        if (response.status === 200 || response.status === 401) {
            logTest('Cart restrictions endpoint responds', true);
        } else {
            logTest('Cart restrictions endpoint responds', false, `Status: ${response.status}`);
        }
    } catch (error) {
        logTest('Cart restrictions endpoint responds', false, error.message);
    }
}

async function testDatabaseConnection() {
    console.log('\n🧪 Testing Database Connection...');
    
    try {
        await mongoose.connect(config.database, {
            useNewUrlParser: true,
            useUnifiedTopology: true,
            serverSelectionTimeoutMS: 5000
        });
        
        // Test if State model has isDomestic field
        const { State } = mongoose.models;
        if (!State) {
            // Load models
            require('./backend/src/models/state');
        }
        
        const sampleState = await mongoose.models.State.findOne();
        if (sampleState && 'isDomestic' in sampleState.toObject()) {
            logTest('State model has isDomestic field', true);
        } else {
            logTest('State model has isDomestic field', false, 'Field not found');
        }
        
        await mongoose.disconnect();
    } catch (error) {
        logTest('Database connection and model check', false, error.message);
    }
}

async function runTests() {
    console.log('🚀 Starting THC Filtering Implementation Tests\n');
    console.log(`Testing against: ${BASE_URL}\n`);
    
    await testDatabaseConnection();
    await testPublicStoreAccess();
    await testCoaWarningEndpoint();
    await testProductFiltering();
    await testCartValidation();
    
    console.log('\n📊 Test Results:');
    console.log(`✅ Passed: ${TEST_RESULTS.passed}`);
    console.log(`❌ Failed: ${TEST_RESULTS.failed}`);
    console.log(`📝 Total: ${TEST_RESULTS.tests.length}`);
    
    if (TEST_RESULTS.failed > 0) {
        console.log('\n❌ Failed Tests:');
        TEST_RESULTS.tests
            .filter(test => !test.passed)
            .forEach(test => console.log(`  - ${test.name}: ${test.message}`));
    }
    
    process.exit(TEST_RESULTS.failed > 0 ? 1 : 0);
}

// Run tests if this file is executed directly
if (require.main === module) {
    runTests().catch(error => {
        console.error('Test runner error:', error);
        process.exit(1);
    });
}

module.exports = { runTests, testPublicStoreAccess, testCoaWarningEndpoint };
